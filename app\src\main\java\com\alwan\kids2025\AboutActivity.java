package com.alwan.kids2025;

import android.os.Bundle;
import androidx.appcompat.widget.Toolbar;

public class AboutActivity extends BaseLocalizedActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_about);

        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle(getString(R.string.about_title));
        }

        // Setup navigation drawer
        setUpNavView();

        // Setup bottom navigation
        BottomNavigationHelperSimple.setupBottomNavigation(this, BottomNavigationHelperSimple.TAB_MORE);
    }

    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return true;
    }

    @Override
    protected boolean useToolbar() {
        return true;
    }

    protected boolean useDrawerToggle() {
        return false;
    }
}
