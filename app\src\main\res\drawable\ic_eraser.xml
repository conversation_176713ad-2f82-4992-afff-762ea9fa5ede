<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="#F44336" />
            <stroke android:width="2dp" android:color="#C62828" />
        </shape>
    </item>
    
    <!-- حالة التحديد -->
    <item android:state_selected="true">
        <shape android:shape="oval">
            <solid android:color="#EF5350" />
            <stroke android:width="3dp" android:color="#B71C1C" />
        </shape>
    </item>
    
    <!-- الحالة العادية -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#FFEBEE" />
            <stroke android:width="1dp" android:color="#F44336" />
        </shape>
    </item>
</selector>
