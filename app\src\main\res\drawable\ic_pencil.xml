<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="#FF9800" />
            <stroke android:width="2dp" android:color="#E65100" />
        </shape>
    </item>
    
    <!-- حالة التحديد -->
    <item android:state_selected="true">
        <shape android:shape="oval">
            <solid android:color="#FFB74D" />
            <stroke android:width="3dp" android:color="#BF360C" />
        </shape>
    </item>
    
    <!-- الحالة العادية -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#FFF3E0" />
            <stroke android:width="1dp" android:color="#FF9800" />
        </shape>
    </item>
</selector>
