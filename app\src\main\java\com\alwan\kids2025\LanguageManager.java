package com.alwan.kids2025;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.util.DisplayMetrics;
import android.view.View;

import java.util.Locale;

/**
 * Comprehensive Language Manager for handling language switching and RTL support
 */
public class LanguageManager {
    
    private static final String PREF_NAME = "app_settings";
    private static final String KEY_LANGUAGE = "app_language";
    private static final String LANGUAGE_ENGLISH = "en";
    private static final String LANGUAGE_ARABIC = "ar";
    
    private static LanguageManager instance;
    private SharedPreferences preferences;
    
    private LanguageManager(Context context) {
        preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }
    
    public static LanguageManager getInstance(Context context) {
        if (instance == null) {
            instance = new LanguageManager(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * Get the currently selected language
     */
    public String getCurrentLanguage() {
        return preferences.getString(KEY_LANGUAGE, LANGUAGE_ARABIC);
    }

    /**
     * Check if current language is Arabic
     */
    public boolean isArabic() {
        return LANGUAGE_ARABIC.equals(getCurrentLanguage());
    }

    /**
     * Check if current language is English
     */
    public boolean isEnglish() {
        return LANGUAGE_ENGLISH.equals(getCurrentLanguage());
    }

    /**
     * Set the application language
     */
    public void setLanguage(String languageCode) {
        if (LANGUAGE_ENGLISH.equals(languageCode) || LANGUAGE_ARABIC.equals(languageCode)) {
            preferences.edit().putString(KEY_LANGUAGE, languageCode).apply();
        }
    }
    
    /**
     * Apply language configuration to context
     */
    public Context applyLanguage(Context context) {
        String language = getCurrentLanguage();
        return updateResources(context, language);
    }
    
    /**
     * Apply language configuration to context with specific language
     */
    public Context applyLanguage(Context context, String languageCode) {
        return updateResources(context, languageCode);
    }
    
    /**
     * Update resources with the specified language
     */
    private Context updateResources(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);
        
        Configuration configuration = new Configuration(context.getResources().getConfiguration());
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            configuration.setLocale(locale);
            configuration.setLayoutDirection(locale);
        } else {
            configuration.locale = locale;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                configuration.setLayoutDirection(locale);
            }
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return context.createConfigurationContext(configuration);
        } else {
            Resources resources = context.getResources();
            DisplayMetrics displayMetrics = resources.getDisplayMetrics();
            resources.updateConfiguration(configuration, displayMetrics);
            return context;
        }
    }
    
    /**
     * Change language and restart activity
     */
    public void changeLanguage(Activity activity, String languageCode) {
        // Save the new language
        setLanguage(languageCode);
        
        // Apply the language change
        applyLanguage(activity, languageCode);
        
        // Restart the activity to apply changes
        restartActivity(activity);
    }
    
    /**
     * Restart the current activity
     */
    private void restartActivity(Activity activity) {
        Intent intent = activity.getIntent();
        activity.finish();
        activity.startActivity(intent);
        activity.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }
    
    /**
     * Get layout direction for current language
     */
    public int getLayoutDirection() {
        if (isArabic()) {
            return View.LAYOUT_DIRECTION_RTL;
        } else {
            return View.LAYOUT_DIRECTION_LTR;
        }
    }
    
    /**
     * Apply RTL/LTR layout direction to a view
     */
    public void applyLayoutDirection(View view) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            view.setLayoutDirection(getLayoutDirection());
        }
    }
    
    /**
     * Get language display name
     */
    public String getLanguageDisplayName(String languageCode) {
        if (LANGUAGE_ARABIC.equals(languageCode)) {
            return "العربية";
        } else {
            return "English";
        }
    }

    /**
     * Get localized language display name
     */
    public String getLocalizedLanguageDisplayName(Context context, String languageCode) {
        if (LANGUAGE_ARABIC.equals(languageCode)) {
            return context.getString(R.string.language_arabic);
        } else {
            return context.getString(R.string.language_english);
        }
    }

    /**
     * Get available languages
     */
    public String[] getAvailableLanguages() {
        return new String[]{LANGUAGE_ENGLISH, LANGUAGE_ARABIC};
    }

    /**
     * Get available language display names
     */
    public String[] getAvailableLanguageNames() {
        return new String[]{"English", "العربية"};
    }

    /**
     * Get available language display names (localized)
     */
    public String[] getLocalizedLanguageNames(Context context) {
        return new String[]{
            context.getString(R.string.language_english),
            context.getString(R.string.language_arabic)
        };
    }

    /**
     * Get language index in available languages array
     */
    public int getLanguageIndex(String languageCode) {
        if (LANGUAGE_ARABIC.equals(languageCode)) {
            return 1;
        } else {
            return 0; // Default to English
        }
    }
}
