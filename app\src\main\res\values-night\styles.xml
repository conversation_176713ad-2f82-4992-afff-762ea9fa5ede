<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Dark Theme -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.DarkActionBar">
        <item name="colorPrimary">@color/modern_primary</item>
        <item name="colorPrimaryDark">@color/status_bar_color</item>
        <item name="colorAccent">@color/modern_accent</item>
        <item name="android:windowBackground">@color/background_color</item>
        <item name="android:colorBackground">@color/background_color</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="android:statusBarColor">@color/status_bar_color</item>
        <item name="android:navigationBarColor">@color/navigation_bar_color</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>

    <!-- Dark Theme for Settings -->
    <style name="SettingsTheme" parent="AppTheme">
        <item name="android:windowBackground">@color/background_color</item>
        <item name="colorPrimary">@color/modern_primary</item>
        <item name="colorAccent">@color/modern_accent</item>
    </style>

    <!-- Dark Switch Style -->
    <style name="ModernSwitchStyle" parent="Widget.AppCompat.CompoundButton.Switch">
        <item name="colorControlActivated">@color/modern_accent</item>
        <item name="colorSwitchThumbNormal">@color/text_secondary</item>
        <item name="android:colorForeground">@color/text_primary</item>
    </style>

    <!-- Dark Card Style -->
    <style name="DarkCardStyle" parent="CardView">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardElevation">8dp</item>
        <item name="cardCornerRadius">12dp</item>
    </style>

    <!-- Dark Button Style -->
    <style name="DarkButtonStyle" parent="Widget.AppCompat.Button">
        <item name="android:background">@color/button_background</item>
        <item name="android:textColor">@color/button_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/blabeloo</item>
    </style>

    <!-- Dark Text Styles -->
    <style name="DarkTitleText">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/blabeloo</item>
    </style>

    <style name="DarkBodyText">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/blabeloo</item>
    </style>

    <!-- Dark Navigation Style -->
    <style name="DarkNavigationStyle">
        <item name="android:background">@color/card_background</item>
        <item name="android:elevation">8dp</item>
    </style>
</resources>
