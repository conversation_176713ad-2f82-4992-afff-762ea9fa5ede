package com.alwan.kids2025;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;
import androidx.cardview.widget.CardView;

public class ExitConfirmationDialog {

    public interface OnExitListener {
        void onConfirmExit();
        void onCancelExit();
    }

    public static void show(Context context, OnExitListener listener) {
        Dialog dialog = new Dialog(context);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_exit_confirmation);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        dialog.setCancelable(true);

        // Initialize views
        TextView titleText = dialog.findViewById(R.id.dialog_title);
        TextView messageText = dialog.findViewById(R.id.dialog_message);
        Button btnCancel = dialog.findViewById(R.id.btn_cancel);
        Button btnExit = dialog.findViewById(R.id.btn_exit);
        CardView dialogCard = dialog.findViewById(R.id.dialog_card);

        // Set text
        titleText.setText("تأكيد الخروج");
        messageText.setText("هل أنت متأكد من أنك تريد الخروج من التطبيق؟\nسيتم حفظ تقدمك تلقائياً.");

        // Set button listeners
        btnCancel.setOnClickListener(v -> {
            dialog.dismiss();
            if (listener != null) {
                listener.onCancelExit();
            }
        });

        btnExit.setOnClickListener(v -> {
            dialog.dismiss();
            if (listener != null) {
                listener.onConfirmExit();
            }
        });

        // Show dialog with animation
        dialog.show();
        
        // Add entrance animation
        if (dialogCard != null) {
            dialogCard.setScaleX(0.8f);
            dialogCard.setScaleY(0.8f);
            dialogCard.setAlpha(0f);
            dialogCard.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .alpha(1f)
                    .setDuration(300)
                    .start();
        }
    }

    public static void showWithCustomMessage(Context context, String title, String message, OnExitListener listener) {
        Dialog dialog = new Dialog(context);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_exit_confirmation);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        dialog.setCancelable(true);

        // Initialize views
        TextView titleText = dialog.findViewById(R.id.dialog_title);
        TextView messageText = dialog.findViewById(R.id.dialog_message);
        Button btnCancel = dialog.findViewById(R.id.btn_cancel);
        Button btnExit = dialog.findViewById(R.id.btn_exit);
        CardView dialogCard = dialog.findViewById(R.id.dialog_card);

        // Set custom text
        titleText.setText(title);
        messageText.setText(message);

        // Set button listeners
        btnCancel.setOnClickListener(v -> {
            dialog.dismiss();
            if (listener != null) {
                listener.onCancelExit();
            }
        });

        btnExit.setOnClickListener(v -> {
            dialog.dismiss();
            if (listener != null) {
                listener.onConfirmExit();
            }
        });

        // Show dialog with animation
        dialog.show();
        
        // Add entrance animation
        if (dialogCard != null) {
            dialogCard.setScaleX(0.8f);
            dialogCard.setScaleY(0.8f);
            dialogCard.setAlpha(0f);
            dialogCard.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .alpha(1f)
                    .setDuration(300)
                    .start();
        }
    }
}
