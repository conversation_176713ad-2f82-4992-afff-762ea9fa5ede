package com.alwan.kids2025;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

/**
 * Modern RecyclerView Adapter with Material Design 3 for coloring images
 */
public class ModernGridAdapter extends RecyclerView.Adapter<ModernGridAdapter.ViewHolder> {
    
    private Context context;
    private List<ImageItem> imageItems;
    private int categoryCode;
    private FavoritesManager favoritesManager;

    public ModernGridAdapter(Context context, List<ImageItem> imageItems, int categoryCode) {
        this.context = context;
        this.imageItems = imageItems;
        this.categoryCode = categoryCode;
        this.favoritesManager = new FavoritesManager(context);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.grid_item_layout, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ImageItem item = imageItems.get(position);
        
        // Set image
        holder.image.setImageBitmap(item.getImage());
        
        // Setup image click
        setupImageClick(holder, position);
        
        // Setup favorite button
        setupFavoriteButton(holder, position);
    }

    @Override
    public int getItemCount() {
        return imageItems.size();
    }

    private void setupImageClick(ViewHolder holder, int position) {
        holder.image.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(context, MainActivityClean.class);
                intent.putExtra("code", categoryCode);
                intent.putExtra("position", position + 1);
                context.startActivity(intent);
            } catch (Exception e) {
                Log.e("ModernGridAdapter", "Error opening coloring activity: " + e.getMessage());
                Toast.makeText(context, "خطأ في فتح الصورة", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupFavoriteButton(ViewHolder holder, int position) {
        // Create image ID
        String imageId = "gp" + categoryCode + "_" + (position + 1);
        
        // Update favorite icon based on current state
        updateFavoriteIcon(holder.favoriteIcon, imageId);
        
        // Setup click listener
        holder.favoriteCard.setOnClickListener(v -> {
            try {
                if (favoritesManager.isFavorite(imageId)) {
                    // Remove from favorites
                    favoritesManager.removeFromFavorites(imageId);
                    updateFavoriteIcon(holder.favoriteIcon, imageId);
                    Toast.makeText(context, "تم إزالة الصورة من المفضلة", Toast.LENGTH_SHORT).show();
                } else {
                    // Add to favorites
                    favoritesManager.addToFavorites(imageId);
                    updateFavoriteIcon(holder.favoriteIcon, imageId);
                    Toast.makeText(context, "تم إضافة الصورة للمفضلة", Toast.LENGTH_SHORT).show();
                }
            } catch (Exception e) {
                Log.e("ModernGridAdapter", "Error toggling favorite: " + e.getMessage());
                Toast.makeText(context, "خطأ في إدارة المفضلة", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void updateFavoriteIcon(ImageView favoriteIcon, String imageId) {
        if (favoritesManager.isFavorite(imageId)) {
            favoriteIcon.setImageResource(R.drawable.ic_favorite_filled);
            favoriteIcon.setColorFilter(ContextCompat.getColor(context, R.color.red));
        } else {
            favoriteIcon.setImageResource(R.drawable.ic_favorite_border);
            favoriteIcon.setColorFilter(ContextCompat.getColor(context, R.color.text_secondary));
        }
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView image;
        CardView favoriteCard;
        ImageView favoriteIcon;

        ViewHolder(View itemView) {
            super(itemView);
            image = itemView.findViewById(R.id.image);
            favoriteCard = itemView.findViewById(R.id.favoriteCard);
            favoriteIcon = itemView.findViewById(R.id.favoriteIcon);
        }
    }
}
