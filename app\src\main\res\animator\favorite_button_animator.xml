<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed State Animation -->
    <item android:state_pressed="true">
        <set>
            <objectAnimator
                android:duration="100"
                android:interpolator="@android:interpolator/fast_out_slow_in"
                android:propertyName="translationZ"
                android:valueTo="16dp"
                android:valueType="floatType" />
            <objectAnimator
                android:duration="100"
                android:interpolator="@android:interpolator/fast_out_slow_in"
                android:propertyName="scaleX"
                android:valueTo="1.1"
                android:valueType="floatType" />
            <objectAnimator
                android:duration="100"
                android:interpolator="@android:interpolator/fast_out_slow_in"
                android:propertyName="scaleY"
                android:valueTo="1.1"
                android:valueType="floatType" />
        </set>
    </item>
    
    <!-- Default State Animation -->
    <item>
        <set>
            <objectAnimator
                android:duration="150"
                android:interpolator="@android:interpolator/fast_out_slow_in"
                android:propertyName="translationZ"
                android:valueTo="8dp"
                android:valueType="floatType" />
            <objectAnimator
                android:duration="150"
                android:interpolator="@android:interpolator/fast_out_slow_in"
                android:propertyName="scaleX"
                android:valueTo="1.0"
                android:valueType="floatType" />
            <objectAnimator
                android:duration="150"
                android:interpolator="@android:interpolator/fast_out_slow_in"
                android:propertyName="scaleY"
                android:valueTo="1.0"
                android:valueType="floatType" />
        </set>
    </item>
    
</selector>
