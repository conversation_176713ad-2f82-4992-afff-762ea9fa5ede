<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/modern_primary_dark"
                android:endColor="@color/modern_primary"
                android:angle="45" />
            <corners android:radius="16dp" />
            <stroke
                android:width="2dp"
                android:color="@color/modern_accent" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/modern_primary"
                android:endColor="@color/modern_primary_dark"
                android:angle="45" />
            <corners android:radius="16dp" />
        </shape>
    </item>
</selector>
