<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="320dp"
    android:orientation="vertical"
    android:background="@drawable/premium_nav_header_background"
    android:gravity="center"
    android:padding="24dp">

    <!-- Decorative Background Elements -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Background Pattern -->
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/premium_nav_pattern"
            android:alpha="0.1"
            android:scaleType="centerCrop" />

        <!-- Content Container -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center">

            <!-- Logo Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="140dp"
                android:layout_height="140dp"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="70dp"
                app:cardElevation="20dp"
                app:cardBackgroundColor="@android:color/white">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <!-- Logo Background -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/premium_logo_background" />

                    <!-- Logo Image -->
                    <ImageView
                        android:id="@+id/nav_logo"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="24dp"
                        android:src="@drawable/small_logo"
                        android:scaleType="centerInside" />

                </FrameLayout>

            </androidx.cardview.widget.CardView>

            <!-- App Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textColor="@android:color/white"
                android:textSize="28sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo"
                android:layout_marginBottom="8dp"
                android:shadowColor="#80000000"
                android:shadowDx="2"
                android:shadowDy="2"
                android:shadowRadius="6"
                android:letterSpacing="0.05" />

            <!-- Subtitle -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="تطبيق التلوين العصري للأطفال"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:fontFamily="@font/blabeloo"
                android:alpha="0.95"
                android:shadowColor="#80000000"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="3"
                android:gravity="center" />

            <!-- Version Info -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="الإصدار 2.0 - Premium"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:fontFamily="@font/blabeloo"
                android:alpha="0.8"
                android:background="@drawable/premium_version_badge"
                android:padding="8dp" />

        </LinearLayout>

        <!-- Decorative Elements -->
        <ImageView
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="top|end"
            android:layout_margin="20dp"
            android:src="@drawable/ic_flowers_modern"
            android:alpha="0.3"
            android:tint="@android:color/white" />

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="bottom|start"
            android:layout_margin="20dp"
            android:src="@drawable/ic_cartoons_modern"
            android:alpha="0.2"
            android:tint="@android:color/white" />

        <ImageView
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_gravity="center|start"
            android:layout_marginStart="15dp"
            android:src="@drawable/ic_animals_modern"
            android:alpha="0.15"
            android:tint="@android:color/white" />

    </FrameLayout>

</LinearLayout>
