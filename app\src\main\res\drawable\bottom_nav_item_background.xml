<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/ripple_color" />
            <corners android:radius="20dp" />
            <stroke
                android:width="1dp"
                android:color="@color/modern_primary" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/modern_accent_light" />
            <corners android:radius="20dp" />
            <stroke
                android:width="1dp"
                android:color="@color/modern_accent" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="20dp" />
        </shape>
    </item>
</selector>
