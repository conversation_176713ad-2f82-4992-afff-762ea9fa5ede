package com.alwan.kids2025;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.Paint;
import android.os.Bundle;
import android.widget.RelativeLayout;

public class MainActivitySimple extends Activity {
    private MyViewSimple myView;
    private Paint paint;
    private RelativeLayout drawingLayout;
    private int code = 1;
    private int position = 1;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // استخراج البيانات الممررة
        Bundle extras = getIntent().getExtras();
        if (extras != null) {
            code = extras.getInt("code", 1);
            position = extras.getInt("position", 1);
        }

        // تهيئة Paint
        initializePaint();

        // تهيئة المكونات
        drawingLayout = findViewById(R.id.relative_layout);

        // إنشاء MyView
        myView = new MyViewSimple(this);
        drawingLayout.addView(myView);
    }

    /**
     * تهيئة Paint للرسم
     */
    private void initializePaint() {
        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setDither(true);
        paint.setColor(Color.BLACK);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeJoin(Paint.Join.ROUND);
        paint.setStrokeCap(Paint.Cap.ROUND);
        paint.setStrokeWidth(15);

        // تمرير Paint إلى MyView
        updateMyViewPaint();
    }

    /**
     * تحديث Paint في MyView
     */
    private void updateMyViewPaint() {
        if (myView != null && paint != null) {
            myView.updatePaint(paint);
        }
    }
}
