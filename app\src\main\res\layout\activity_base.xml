<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:layoutDirection="locale">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="?attr/colorPrimary"
            android:theme="@style/ThemeOverlay.AppCompat.Dark" />

        <FrameLayout
            android:id="@+id/activity_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/navigationView"
        android:layout_width="320dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/modern_surface"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/premium_nav_header"
        app:itemIconTint="@drawable/premium_nav_item_color"
        app:itemTextAppearance="@style/PremiumNavTextAppearance"
        app:itemTextColor="@drawable/premium_nav_item_color"
        app:itemBackground="@drawable/premium_nav_item_background"
        app:itemHorizontalPadding="20dp"
        app:itemVerticalPadding="12dp"
        app:itemIconPadding="16dp"
        app:itemShapeFillColor="@color/modern_primary"
        app:itemShapeInsetStart="12dp"
        app:itemShapeInsetEnd="12dp"
        app:itemShapeInsetTop="4dp"
        app:itemShapeInsetBottom="4dp"
        app:itemShapeAppearance="@style/PremiumNavItemShape"
        app:menu="@menu/premium_nav_drawer"
        tools:ignore="UnusedAttribute" />
</androidx.drawerlayout.widget.DrawerLayout>
