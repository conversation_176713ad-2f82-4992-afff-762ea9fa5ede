{"logs": [{"outputFile": "com.alwan.kids2025.app-mergeDebugResources-62:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3980eaeb5be19d054e57b3fb1dced5a4\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "96,97,98,99,100,101,102,347", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6599,6692,6794,6889,6992,7095,7197,25402", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "6687,6789,6884,6987,7090,7192,7306,25498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\624bef4dd02dccf90d82100848c6b989\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,327", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "570,678,782,889,971,1072,1186,1266,1345,1436,1529,1621,1715,1815,1908,2003,2096,2187,2281,2360,2465,2563,2661,2769,2869,2972,3127,24006", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "673,777,884,966,1067,1181,1261,1340,1431,1524,1616,1710,1810,1903,1998,2091,2182,2276,2355,2460,2558,2656,2764,2864,2967,3122,3219,24083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fe7112e8008236a43395ec4960536a6a\\transformed\\preference-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,265,351,484,653,735", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "167,260,346,479,648,730,810"}, "to": {"startLines": "135,159,278,297,348,363,364", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "10338,12102,20595,22142,25503,26464,26546", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "10400,12190,20676,22270,25667,26541,26621"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0f6c2614959f9583c84a75a13e2222db\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "136,164,165,166", "startColumns": "4,4,4,4", "startOffsets": "10405,12455,12553,12661", "endColumns": "99,97,107,101", "endOffsets": "10500,12548,12656,12758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6d4e9d128fcd43e5a6e86ce4f6eb5926\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "116,117,118,119,120,121,122,123,125,126,127,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8187,8291,8435,8557,8662,8800,8928,9039,9271,9408,9512,9662,9784,9923,10069,10133,10199", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "8286,8430,8552,8657,8795,8923,9034,9136,9403,9507,9657,9779,9918,10064,10128,10194,10278"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\041427d19b31fae88f13d56248b66aa4\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "2,88,89,90,91,92,106,107,113,146,147,163,186,191,198,199,200,201,202,203,204,205,206,207,208,209,210,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,295,328,329,340", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,6026,6104,6180,6264,6356,7451,7552,7949,11096,11155,12364,14022,14318,14748,14848,14911,14976,15037,15105,15167,15225,15339,15399,15460,15517,15590,15945,16026,16118,16225,16323,16403,16551,16632,16713,16841,16930,17006,17059,17113,17179,17257,17337,17408,17490,17562,17636,17709,17779,17888,17979,18050,18140,18235,18309,18392,18485,18534,18615,18684,18770,18855,18917,18981,19044,19113,19222,19332,19429,19529,19586,22013,24088,24167,24954", "endLines": "9,88,89,90,91,92,106,107,113,146,147,163,186,191,198,199,200,201,202,203,204,205,206,207,208,209,210,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,295,328,329,340", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,6099,6175,6259,6351,6434,7547,7666,8021,11150,11213,12450,14086,14380,14843,14906,14971,15032,15100,15162,15220,15334,15394,15455,15512,15585,15708,16021,16113,16220,16318,16398,16546,16627,16708,16836,16925,17001,17054,17108,17174,17252,17332,17403,17485,17557,17631,17704,17774,17883,17974,18045,18135,18230,18304,18387,18480,18529,18610,18679,18765,18850,18912,18976,19039,19108,19217,19327,19424,19524,19581,19639,22088,24162,24237,25025"}}, {"source": "Z:\\alwan6\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "6,10,169,168,14,90,47,44,3,2,50,228,153,237,235,35,183,139,127,193,192,158,194,71,167,1,241,72,191,187,63,98,97,186,62,96,95,229,188,73,13,138,126,52,133,206,207,230,108,107,263,65,102,101,118,200,199,38,264,262,212,210,211,45,43,41,40,42,78,76,77,154,148,231,185,106,105,151,226,232,225,195,196,198,197,137,125,140,128,36,39,100,99,67,115,114,247,122,203,121,66,46,9,258,157,163,162,145,142,130,82,5,4,84,81,85,37,227,152,146,259,51,55,54,57,56,12,8,69,176,175,68,33,111,113,112,116,117,11,254,174,173,257,238,236,70,256,255,7,147,60,164,166,165,172,171,180,170,179,64,34,61,94,93,49,48,159,216,220,218,219,215,141,129,249,245,248,250,251,246,104,103,184,265,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "285,482,8208,8151,641,4164,2243,1986,117,68,2405,11364,7397,11909,11766,1323,9061,6807,6301,9624,9561,7609,9864,3507,8091,17,12071,3561,9492,9346,3042,4638,4567,9262,2979,4486,4406,11460,9404,3616,599,6761,6246,2508,6545,10511,10567,11519,5383,5302,13118,3148,4928,4859,5941,10286,10221,1493,13174,13047,10767,10654,10712,2051,1914,1722,1596,1831,3869,3697,3757,7480,7211,11576,9193,5215,5129,7294,11229,11631,11170,9927,9999,10153,10078,6717,6193,6854,6357,1370,1541,4784,4710,3261,5705,5644,12356,6102,10414,6045,3200,2155,439,12915,7568,7832,7765,7016,6948,6464,3989,224,162,4042,3942,4093,1441,11298,7341,7071,12968,2452,2621,2562,2765,2683,561,389,3379,8674,8609,3316,1218,5497,5596,5545,5777,5861,521,12656,8536,8478,12868,11969,11834,3438,12775,12706,341,7135,2871,7907,8023,7969,8405,8345,8954,8280,8778,3096,1271,2919,4328,4251,2349,2294,7665,10925,11080,10980,11033,10871,6897,6408,12463,12207,12399,12526,12575,12282,5063,4998,9121,13231,6607", "endLines": "6,10,169,168,28,90,47,44,3,2,50,228,153,237,235,35,183,139,127,193,192,158,194,71,167,1,241,72,191,187,63,98,97,186,62,96,95,229,188,73,13,138,126,52,133,206,207,230,108,107,263,65,102,101,118,200,199,38,264,262,212,210,211,45,43,41,40,42,78,76,77,154,148,231,185,106,105,151,226,232,225,195,196,198,197,137,125,140,128,36,39,100,99,67,115,114,247,122,203,121,66,46,9,258,157,163,162,145,142,130,82,5,4,84,81,85,37,227,152,146,259,51,55,54,57,56,12,8,69,176,175,68,33,111,113,112,116,117,11,254,174,173,257,238,236,70,256,255,7,147,60,164,166,165,172,171,180,170,179,64,34,61,94,93,49,48,159,216,220,218,219,215,141,129,249,245,248,250,251,246,104,103,184,265,134", "endColumns": "54,37,70,55,59,51,49,63,43,47,45,94,81,58,66,45,58,45,54,238,61,54,61,52,58,49,62,53,67,56,52,70,69,82,61,79,78,57,49,51,40,44,53,50,60,54,54,55,80,79,54,50,68,67,69,95,63,46,55,69,68,56,53,102,70,107,124,81,44,58,110,58,55,53,67,85,84,45,67,99,57,70,77,66,73,42,51,41,49,69,53,73,72,53,70,59,41,51,65,55,59,86,41,51,39,73,65,53,42,51,49,59,60,49,45,43,50,64,54,62,46,54,60,57,79,80,36,48,57,75,63,61,51,46,46,49,82,78,38,48,71,56,45,66,73,67,91,67,46,74,46,60,66,52,71,58,62,63,174,50,50,58,76,75,54,53,61,51,51,51,45,52,49,54,61,73,62,47,49,72,64,63,70,65,77", "endOffsets": "335,515,8274,8202,1154,4211,2288,2045,156,111,2446,11454,7474,11963,11828,1364,9115,6848,6351,9858,9618,7659,9921,3555,8145,62,12129,3610,9555,9398,3090,4704,4632,9340,3036,4561,4480,11513,9449,3663,635,6801,6295,2554,6601,10561,10617,11570,5459,5377,13168,3194,4992,4922,6006,10377,10280,1535,13225,13112,10831,10706,10761,2149,1980,1825,1716,1908,3909,3751,3863,7534,7262,11625,9256,5296,5209,7335,11292,11726,11223,9993,10072,10215,10147,6755,6240,6891,6402,1435,1590,4853,4778,3310,5771,5699,12393,6149,10475,6096,3255,2237,476,12962,7603,7901,7826,7065,6986,6511,4034,279,218,4087,3983,4132,1487,11358,7391,7129,13010,2502,2677,2615,2840,2759,593,433,3432,8745,8668,3373,1265,5539,5638,5590,5855,5935,555,12700,8603,8530,12909,12031,11903,3501,12862,12769,383,7205,2913,7963,8085,8017,8472,8399,9012,8339,8948,3142,1317,2973,4400,4322,2399,2343,7722,10972,11127,11027,11074,10919,6942,6458,12520,12276,12457,12569,12620,12350,5123,5057,9187,13292,6680"}, "to": {"startLines": "10,38,39,40,41,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,93,94,95,103,104,105,108,109,110,111,112,114,115,134,137,138,139,140,141,142,143,144,145,148,149,150,151,152,153,154,155,156,157,158,160,161,162,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,187,188,189,190,192,193,194,195,196,197,211,212,213,214,260,264,265,266,267,268,269,270,271,272,273,274,275,276,277,279,280,281,282,294,296,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,325,326,330,331,332,333,334,335,336,337,338,339,341,342,343,344,345,346,349,350,351,352,353,354,355,356,357,358,359,360,361,362,365,366,367,368,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3224,3262,3333,3389,3893,3945,3995,4059,4103,4151,4197,4292,4374,4433,4500,4546,4605,4651,4706,4945,5007,5062,5124,5177,5236,5286,5349,5403,5471,5528,5581,5652,5722,5805,5867,5947,6439,6497,6547,7311,7352,7397,7671,7722,7783,7838,7893,8026,8107,10283,10505,10556,10625,10693,10763,10859,10923,10970,11026,11218,11287,11344,11398,11501,11572,11680,11805,11887,11932,11991,12195,12254,12310,12763,12831,12917,13002,13048,13116,13216,13274,13345,13423,13490,13564,13607,13659,13701,13751,13821,13875,13949,14091,14145,14216,14276,14385,14437,14503,14559,14619,14706,15713,15765,15805,15879,19644,19853,19896,19948,19998,20058,20119,20169,20215,20259,20310,20375,20430,20493,20540,20681,20742,20800,20880,21976,22093,22275,22333,22409,22473,22535,22587,22634,22681,22731,22814,22893,22932,22981,23053,23110,23156,23223,23297,23365,23457,23884,23931,24242,24289,24350,24417,24470,24542,24601,24664,24728,24903,25030,25081,25140,25217,25293,25348,25672,25734,25786,25838,25890,25936,25989,26039,26094,26156,26230,26293,26341,26391,26626,26691,26755,26826,26952", "endLines": "10,38,39,40,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,93,94,95,103,104,105,108,109,110,111,112,114,115,134,137,138,139,140,141,142,143,144,145,148,149,150,151,152,153,154,155,156,157,158,160,161,162,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,187,188,189,190,192,193,194,195,196,197,211,212,213,214,260,264,265,266,267,268,269,270,271,272,273,274,275,276,277,279,280,281,282,294,296,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,325,326,330,331,332,333,334,335,336,337,338,339,341,342,343,344,345,346,349,350,351,352,353,354,355,356,357,358,359,360,361,362,365,366,367,368,370", "endColumns": "54,37,70,55,59,51,49,63,43,47,45,94,81,58,66,45,58,45,54,238,61,54,61,52,58,49,62,53,67,56,52,70,69,82,61,79,78,57,49,51,40,44,53,50,60,54,54,55,80,79,54,50,68,67,69,95,63,46,55,69,68,56,53,102,70,107,124,81,44,58,110,58,55,53,67,85,84,45,67,99,57,70,77,66,73,42,51,41,49,69,53,73,72,53,70,59,41,51,65,55,59,86,41,51,39,73,65,53,42,51,49,59,60,49,45,43,50,64,54,62,46,54,60,57,79,80,36,48,57,75,63,61,51,46,46,49,82,78,38,48,71,56,45,66,73,67,91,67,46,74,46,60,66,52,71,58,62,63,174,50,50,58,76,75,54,53,61,51,51,51,45,52,49,54,61,73,62,47,49,72,64,63,70,65,77", "endOffsets": "565,3257,3328,3384,3888,3940,3990,4054,4098,4146,4192,4287,4369,4428,4495,4541,4600,4646,4701,4940,5002,5057,5119,5172,5231,5281,5344,5398,5466,5523,5576,5647,5717,5800,5862,5942,6021,6492,6542,6594,7347,7392,7446,7717,7778,7833,7888,7944,8102,8182,10333,10551,10620,10688,10758,10854,10918,10965,11021,11091,11282,11339,11393,11496,11567,11675,11800,11882,11927,11986,12097,12249,12305,12359,12826,12912,12997,13043,13111,13211,13269,13340,13418,13485,13559,13602,13654,13696,13746,13816,13870,13944,14017,14140,14211,14271,14313,14432,14498,14554,14614,14701,14743,15760,15800,15874,15940,19693,19891,19943,19993,20053,20114,20164,20210,20254,20305,20370,20425,20488,20535,20590,20737,20795,20875,20956,22008,22137,22328,22404,22468,22530,22582,22629,22676,22726,22809,22888,22927,22976,23048,23105,23151,23218,23292,23360,23452,23520,23926,24001,24284,24345,24412,24465,24537,24596,24659,24723,24898,24949,25076,25135,25212,25288,25343,25397,25729,25781,25833,25885,25931,25984,26034,26089,26151,26225,26288,26336,26386,26459,26686,26750,26821,26887,27025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c41e8149bb8e825c8201b1dfddeb0e8d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "124", "startColumns": "4", "startOffsets": "9141", "endColumns": "129", "endOffsets": "9266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f13dd0690544d3555e27affebacc963d\\transformed\\jetified-play-services-ads-23.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,342,406,474,573,634,755,871,1006,1059,1116,1228,1313,1351,1430,1462,1493,1536,1604,1644", "endColumns": "40,47,53,63,67,98,60,120,115,134,52,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,405,473,572,633,754,870,1005,1058,1115,1227,1312,1350,1429,1461,1492,1535,1603,1643,1699"}, "to": {"startLines": "261,262,263,283,284,285,286,287,288,289,290,291,292,293,318,319,320,321,322,323,324,369", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19698,19743,19795,20961,21029,21101,21204,21269,21394,21514,21653,21710,21771,21887,23525,23567,23650,23686,23721,23768,23840,26892", "endColumns": "44,51,57,67,71,102,64,124,119,138,56,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "19738,19790,19848,21024,21096,21199,21264,21389,21509,21648,21705,21766,21882,21971,23562,23645,23681,23716,23763,23835,23879,26947"}}]}]}