<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="280dp"
    android:background="@drawable/modern_gradient_background"
    android:orientation="vertical"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <!-- Background Pattern -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/nav_header_pattern"
        android:alpha="0.1" />

    <!-- Content Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- Logo Container -->
        <androidx.cardview.widget.CardView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="60dp"
            app:cardElevation="16dp"
            app:cardBackgroundColor="@android:color/white">

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="20dp"
                android:src="@drawable/small_logo"
                android:scaleType="centerInside" />

        </androidx.cardview.widget.CardView>

        <!-- App Name -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textColor="@android:color/white"
            android:textSize="24sp"
            android:textStyle="bold"
            android:fontFamily="@font/blabeloo"
            android:layout_marginBottom="8dp"
            android:shadowColor="#80000000"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="4" />

        <!-- Subtitle -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_subtitle"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:alpha="0.9"
            android:fontFamily="@font/blabeloo"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2" />

    </LinearLayout>

    <!-- Decorative Elements -->
    <ImageView
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_margin="16dp"
        android:src="@drawable/ic_flowers_modern"
        android:alpha="0.3"
        android:tint="@android:color/white" />

    <ImageView
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentStart="true"
        android:layout_margin="16dp"
        android:src="@drawable/ic_cartoons_modern"
        android:alpha="0.2"
        android:tint="@android:color/white" />

</RelativeLayout>
