<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modern_background">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/modern_gradient_background"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:paddingBottom="96dp">

            <!-- Header Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/favorites_header_background"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginBottom="16dp"
                        android:src="@drawable/ic_favorites_modern"
                        android:tint="@android:color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/favorites_title"
                        android:textColor="@android:color/white"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/favorites_subtitle"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:fontFamily="@font/blabeloo"
                        android:alpha="0.9" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Empty State -->
            <androidx.cardview.widget.CardView
                android:id="@+id/empty_favorites_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="40dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_marginBottom="24dp"
                        android:src="@drawable/ic_empty_heart"
                        android:tint="@color/text_secondary"
                        android:alpha="0.6" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/no_favorite_images"
                        android:textColor="@color/text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/add_favorites_instruction"
                        android:textColor="@color/text_secondary"
                        android:textSize="16sp"
                        android:fontFamily="@font/blabeloo"
                        android:gravity="center"
                        android:layout_marginBottom="24dp" />

                    <!-- Browse Categories Button -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/btn_browse_categories"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:foreground="@drawable/beautiful_ripple_effect"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="25dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@color/modern_accent">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="16dp"
                            android:gravity="center_vertical">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_marginEnd="12dp"
                                android:src="@drawable/ic_categories_modern"
                                android:tint="@android:color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/browse_categories"
                                android:textColor="@android:color/white"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Favorites Grid (Hidden when empty) -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/favorites_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2" />

            <!-- Quick Actions (Hidden when empty) -->
            <LinearLayout
                android:id="@+id/favorites_actions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="16dp"
                android:visibility="gone">

                <!-- Action Buttons Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">

                    <!-- Clear Favorites Button -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/btn_clear_favorites"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:foreground="@drawable/beautiful_ripple_effect"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="6dp"
                        app:cardBackgroundColor="@color/modern_surface">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="16dp"
                            android:gravity="center">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_marginEnd="8dp"
                                android:src="@drawable/ic_clear_modern"
                                android:tint="@color/text_secondary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/clear_favorites"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                    <!-- Export Favorites Button -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/btn_export_favorites"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:foreground="@drawable/beautiful_ripple_effect"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="6dp"
                        app:cardBackgroundColor="@color/modern_accent">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="16dp"
                            android:gravity="center">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_marginEnd="8dp"
                                android:src="@drawable/ic_export_modern"
                                android:tint="@android:color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/export_list"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <!-- Tips Card -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:cardUseCompatPadding="true"
                    app:cardBackgroundColor="#FFF3E0">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="12dp"
                            android:src="@drawable/ic_tip_modern"
                            android:tint="@color/modern_accent" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/favorites_tip"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            android:fontFamily="@font/blabeloo"
                            android:lineSpacingExtra="2dp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Navigation -->
    <include
        layout="@layout/simple_bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
