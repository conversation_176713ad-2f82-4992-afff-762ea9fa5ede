<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:layoutDirection="rtl">

    <!-- Top Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark"
        android:elevation="4dp"
        app:title="@string/app_name"
        app:titleTextColor="@android:color/white"
        app:navigationIcon="?attr/homeAsUpIndicator" />

    <!-- Color Palette Section - Restored and Enhanced -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:background="@drawable/rounded_background"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_horizontal">

        <!-- Colors Grid Container -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <!-- First Row of Colors -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <ImageButton
                    android:id="@+id/deep_orange"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_deep_orange"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Deep Orange Color" />

                <ImageButton
                    android:id="@+id/light_pink"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_light_pink"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Light Pink Color" />

                <ImageButton
                    android:id="@+id/light_green"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_light_green"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Light Green Color" />

                <ImageButton
                    android:id="@+id/yellow"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_yellow"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Yellow Color" />

                <ImageButton
                    android:id="@+id/light_blue"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_light_blue"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Light Blue Color" />

                <ImageButton
                    android:id="@+id/deep_purple"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_deep_purple"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Deep Purple Color" />

                <ImageButton
                    android:id="@+id/light_orange"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_light_orange"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Light Orange Color" />

                <ImageButton
                    android:id="@+id/white"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_white"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="White Color" />
            </LinearLayout>

            <!-- Second Row of Colors -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/red"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_red"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Red Color" />

                <ImageButton
                    android:id="@+id/deep_pink"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_deep_pink"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Deep Pink Color" />

                <ImageButton
                    android:id="@+id/deep_green"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_deep_green"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Deep Green Color" />

                <ImageButton
                    android:id="@+id/light_purple"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_light_purple"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Light Purple Color" />

                <ImageButton
                    android:id="@+id/deep_blue"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_deep_blue"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Deep Blue Color" />

                <ImageButton
                    android:id="@+id/brown"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_brown"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Brown Color" />

                <ImageButton
                    android:id="@+id/gray"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_gray"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Gray Color" />

                <ImageButton
                    android:id="@+id/black"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_margin="3dp"
                    android:background="@drawable/color_black"
                    android:clickable="true"
                    android:focusable="true"
                    android:scaleType="fitXY"
                    android:contentDescription="Black Color" />
            </LinearLayout>
        </LinearLayout>

        <!-- Color Selector Button (Circular) -->
        <ImageButton
            android:id="@+id/select_color"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginStart="12dp"
            android:background="@drawable/select_color"
            android:clickable="true"
            android:focusable="true"
            android:scaleType="fitXY"
            android:contentDescription="Color Picker" />

    </LinearLayout>

    <!-- Spacer -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:visibility="visible" />

    <!-- Modern Canvas Area with Material Design 3 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="8dp"
        app:cardBackgroundColor="@color/surface_variant"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:strokeColor="@color/outline_variant"
        app:strokeWidth="1dp">

        <RelativeLayout
            android:id="@+id/relative_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="8dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:splitMotionEvents="true">

            <ImageView
                android:id="@+id/coringImage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center_horizontal|center_vertical"
                android:adjustViewBounds="true"
                android:scaleType="centerInside"
                android:visibility="gone" />
        </RelativeLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Modern Bottom Ad Banner with Material Design 3 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        app:cardBackgroundColor="@color/surface_primary"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <com.google.android.gms.ads.AdView
            xmlns:ads="http://schemas.android.com/apk/res-auto"
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="4dp"
            ads:adSize="SMART_BANNER"
            ads:adUnitId="@string/banner_unit_id"/>

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
