package com.alwan.kids2025;

import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.media.RingtoneManager;
import android.net.Uri;
import androidx.core.app.NotificationCompat;
import android.util.Log;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

public class MyFirebaseMessagingService extends FirebaseMessagingService {

    private static final String TAG = "MyFirebaseMsgService";
    String data;

    // ✅ دالة التهيئة المضافة
    public static void initialize(Context context) {
        Log.d(TAG, "Firebase Messaging initialized!");

        // بإمكانك هنا تضيف كود تسجيل التوكن أو أي إعدادات أخرى
        // مثال:
        // FirebaseMessaging.getInstance().getToken()
        //     .addOnCompleteListener(task -> {
        //         if (!task.isSuccessful()) {
        //             Log.w(TAG, "Fetching FCM registration token failed", task.getException());
        //             return;
        //         }
        //         String token = task.getResult();
        //         Log.d(TAG, "FCM Token: " + token);
        //     });
    }

    // ✅ استقبال الرسائل
    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        Log.d(TAG, getString(R.string.notification_from) + remoteMessage.getFrom());
        Log.d(TAG, getString(R.string.notification_body) + remoteMessage.getNotification().getBody());
        data = remoteMessage.getNotification().getBody();
        sendNotification(remoteMessage.getNotification().getBody());
    }

    // ✅ إرسال إشعار للمستخدم
    private void sendNotification(String messageBody) {
        Intent intent = new Intent(this, Categories.class);
        intent.putExtra("data", data);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent,
                PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE);

        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, "default_channel_id")
                .setSmallIcon(R.drawable.small_logo)
                .setContentTitle(getString(R.string.app_name))
                .setContentText(messageBody)
                .setAutoCancel(true)
                .setSound(defaultSoundUri)
                .setContentIntent(pendingIntent);

        NotificationManager notificationManager =
                (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        notificationManager.notify(0, notificationBuilder.build());
    }
}
