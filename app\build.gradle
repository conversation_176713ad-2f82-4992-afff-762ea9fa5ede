apply plugin: 'com.android.application'

android {
    compileSdk 34
    defaultConfig {
        applicationId "com.alwan.kids2025"
        minSdkVersion 23
        targetSdkVersion 34
        versionCode 4
        versionName "4.0"
        multiDexEnabled true

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.alwan.kids2025'
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
        // Removed unsupported option: allWarningsAsErrors
    }

    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:deprecation"
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'org.jetbrains:annotations:26.0.2'
    androidTestImplementation('androidx.test.espresso:espresso-core:3.6.1', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    implementation 'androidx.appcompat:appcompat:1.7.0'
    testImplementation 'junit:junit:4.13.2'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'com.github.ViksaaSkool:AwesomeSplash:v1.0.0'
    implementation ('com.google.android.gms:play-services-ads:23.5.0') {
        exclude group: 'com.google.android.gms', module : 'play-services-ads-identifier'
    }
    implementation 'com.google.android.ump:user-messaging-platform:3.2.0'
    // the BoM for the Firebase platform
    implementation platform('com.google.firebase:firebase-bom:33.6.0')
    implementation ('com.google.firebase:firebase-messaging') {
        exclude group: 'com.google.android.gms', module : 'play-services-ads-identifier'
    }
    implementation ('com.google.firebase:firebase-analytics') {
        exclude group: 'com.google.android.gms', module : 'play-services-ads-identifier'
    }

    implementation 'com.github.QuadFlask:colorpicker:0.0.15' // Corrected version
    // HSV-Alpha Color Picker Library - Advanced color picker with HSV wheel
    implementation 'com.github.martin-stone:hsv-alpha-color-picker-android:3.1.0'
    // Google Play Libraries - Enhanced Features
    implementation 'com.google.android.play:review:2.0.2' // Latest stable version for In-App Reviews
    implementation 'com.google.android.play:app-update:2.1.0' // Latest stable version for In-App Updates
    implementation 'com.google.android.play:asset-delivery:2.3.0' // Asset Delivery for dynamic content
    // Google Tasks API for Play Libraries
    implementation 'com.google.android.gms:play-services-tasks:18.2.0'
    // Add androidx.preference library
    implementation 'androidx.preference:preference:1.2.1'
    // Add AndroidX libraries (removed duplicate appcompat)
    implementation 'androidx.core:core-ktx:1.13.1'
    // OneSignal library (reverted to stable version)
    implementation 'com.onesignal:OneSignal:4.8.6'
    // Updated dependencies (removed duplicates)
    implementation 'androidx.core:core:1.13.1'
    implementation 'androidx.activity:activity:1.9.2'
    // Removed duplicate preference library
}
apply plugin: 'com.google.gms.google-services'
