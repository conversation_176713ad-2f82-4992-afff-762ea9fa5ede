package com.alwan.kids2025;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

import java.util.LinkedList;
import java.util.Queue;
import java.util.ArrayList;
import java.util.List;

public class MyViewSimple extends View {
    private Paint mPaint;
    public Bitmap scaledBitmap;
    public Bitmap workingBitmap; // نسخة قابلة للتعديل من الصورة
    private Context context;
    private Canvas workingCanvas;

    // نظام التراجع المتدرج
    private ArrayList<Bitmap> undoHistory = new ArrayList<>();
    private int maxUndoSteps = 10; // الحد الأقصى لخطوات التراجع
    private int currentUndoIndex = -1;

    // Interface for paint events
    public interface OnPaintListener {
        void onPaintApplied();
    }

    private OnPaintListener paintListener;

    public MyViewSimple(Context context) {
        super(context);
        this.context = context;

        // تهيئة Paint للتلوين
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setColor(Color.BLACK); // اللون الافتراضي

        // تحميل الصورة الافتراضية
        loadDefaultImage();
    }

    /**
     * تحميل صورة افتراضية وإعداد Bitmap للتعديل
     */
    private void loadDefaultImage() {
        try {
            // محاولة تحميل صورة افتراضية
            int resourceId = getResources().getIdentifier("gp1_1", "drawable", context.getPackageName());
            if (resourceId != 0) {
                Bitmap originalBitmap = BitmapFactory.decodeResource(getResources(), resourceId);
                if (originalBitmap != null) {
                    scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, 800, 600, false);
                }
            }
        } catch (Exception e) {
            // في حالة فشل التحميل، إنشاء bitmap فارغ
            scaledBitmap = Bitmap.createBitmap(800, 600, Bitmap.Config.ARGB_8888);
            scaledBitmap.eraseColor(Color.WHITE);
        }

        // إنشاء نسخة قابلة للتعديل
        if (scaledBitmap != null) {
            workingBitmap = scaledBitmap.copy(Bitmap.Config.ARGB_8888, true);
            workingCanvas = new Canvas(workingBitmap);
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        // عرض الصورة المعدلة (مع التلوين)
        if (workingBitmap != null) {
            canvas.drawBitmap(workingBitmap, 0, 0, null);
        } else if (scaledBitmap != null) {
            canvas.drawBitmap(scaledBitmap, 0, 0, null);
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            int x = (int) event.getX();
            int y = (int) event.getY();

            // تطبيق Flood Fill عند النقر
            if (workingBitmap != null && x >= 0 && y >= 0 &&
                x < workingBitmap.getWidth() && y < workingBitmap.getHeight()) {

                Log.d("MyViewSimple", "Flood fill at: " + x + ", " + y + " with color: " + mPaint.getColor());

                // حفظ الحالة الحالية قبل التلوين
                saveCurrentState();

                floodFill(x, y, mPaint.getColor());

                // Notify listener that paint was applied
                if (paintListener != null) {
                    paintListener.onPaintApplied();
                }

                invalidate();
            }
        }
        return true;
    }

    /**
     * تحديث Paint
     */
    public void updatePaint(Paint paint) {
        if (paint != null) {
            mPaint.set(paint);
        }
    }

    /**
     * تعيين مستمع أحداث التلوين
     */
    public void setOnPaintListener(OnPaintListener listener) {
        this.paintListener = listener;
    }

    /**
     * خوارزمية Flood Fill محسنة لملء المناطق المغلقة بالكامل
     */
    private void floodFill(int x, int y, int newColor) {
        if (workingBitmap == null) return;

        int originalColor = workingBitmap.getPixel(x, y);

        // إذا كان اللون الجديد نفس اللون الأصلي، لا نحتاج لفعل شيء
        if (originalColor == newColor) return;

        // إذا كان اللون الأصلي أسود (خط الحدود)، لا نلون
        if (originalColor == Color.BLACK) return;

        int width = workingBitmap.getWidth();
        int height = workingBitmap.getHeight();

        // التحقق من الحدود
        if (x < 0 || x >= width || y < 0 || y >= height) return;

        try {
            // استخدام خوارزمية Scanline Flood Fill المحسنة
            scanlineFloodFill(x, y, originalColor, newColor, width, height);
        } catch (Exception e) {
            Log.e("MyViewSimple", "Error in flood fill: " + e.getMessage());
            // في حالة الخطأ، نطبق تلوين بسيط للنقطة فقط
            workingBitmap.setPixel(x, y, newColor);
        }
    }

    /**
     * خوارزمية Scanline Flood Fill - أكثر كفاءة وأمان
     */
    private void scanlineFloodFill(int x, int y, int originalColor, int newColor, int width, int height) {
        // استخدام Stack بدلاً من Recursion لتجنب Stack Overflow
        java.util.Stack<int[]> stack = new java.util.Stack<>();
        stack.push(new int[]{x, y});

        // حد أقصى للعمليات لتجنب التجمد
        int maxOperations = 50000;
        int operations = 0;

        while (!stack.isEmpty() && operations < maxOperations) {
            int[] point = stack.pop();
            int px = point[0];
            int py = point[1];

            // التحقق من الحدود
            if (px < 0 || px >= width || py < 0 || py >= height) continue;

            // التحقق من اللون
            if (workingBitmap.getPixel(px, py) != originalColor) continue;

            // ملء الخط الأفقي
            int leftX = px;
            int rightX = px;

            // البحث عن أقصى اليسار
            while (leftX > 0 && workingBitmap.getPixel(leftX - 1, py) == originalColor) {
                leftX--;
            }

            // البحث عن أقصى اليمين
            while (rightX < width - 1 && workingBitmap.getPixel(rightX + 1, py) == originalColor) {
                rightX++;
            }

            // ملء الخط الأفقي
            for (int i = leftX; i <= rightX; i++) {
                workingBitmap.setPixel(i, py, newColor);
                operations++;
            }

            // إضافة النقاط العلوية والسفلية للمعالجة
            for (int i = leftX; i <= rightX; i++) {
                // النقطة العلوية
                if (py > 0 && workingBitmap.getPixel(i, py - 1) == originalColor) {
                    stack.push(new int[]{i, py - 1});
                }
                // النقطة السفلية
                if (py < height - 1 && workingBitmap.getPixel(i, py + 1) == originalColor) {
                    stack.push(new int[]{i, py + 1});
                }
            }
        }

        Log.d("MyViewSimple", "Flood fill completed with " + operations + " operations");
    }

    /**
     * تحميل صورة محددة بناءً على الكود والموضع مع تحسين الحجم
     */
    public void loadImageByCodeAndPosition(int code, int position) {
        try {
            // تكوين اسم الصورة
            String imageName = "gp" + code + "_" + position;
            int resourceId = getResources().getIdentifier(imageName, "drawable", context.getPackageName());

            if (resourceId != 0) {
                Bitmap originalBitmap = BitmapFactory.decodeResource(getResources(), resourceId);
                if (originalBitmap != null) {
                    // حساب أبعاد الشاشة المتاحة
                    int screenWidth = getResources().getDisplayMetrics().widthPixels;
                    int screenHeight = getResources().getDisplayMetrics().heightPixels;

                    // تحديد حجم مناسب للصورة (90% من عرض الشاشة)
                    int targetWidth = (int) (screenWidth * 0.9);
                    int targetHeight = (int) (targetWidth * ((float) originalBitmap.getHeight() / originalBitmap.getWidth()));

                    // التأكد من أن الارتفاع لا يتجاوز 70% من ارتفاع الشاشة
                    int maxHeight = (int) (screenHeight * 0.7);
                    if (targetHeight > maxHeight) {
                        targetHeight = maxHeight;
                        targetWidth = (int) (targetHeight * ((float) originalBitmap.getWidth() / originalBitmap.getHeight()));
                    }

                    scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, targetWidth, targetHeight, true);

                    // إنشاء نسخة قابلة للتعديل
                    workingBitmap = scaledBitmap.copy(Bitmap.Config.ARGB_8888, true);
                    workingCanvas = new Canvas(workingBitmap);

                    Log.d("MyViewSimple", "Loaded image: " + imageName + " with size: " + targetWidth + "x" + targetHeight);
                    invalidate();
                }
            } else {
                Log.e("MyViewSimple", "Image not found: " + imageName);
            }
        } catch (Exception e) {
            Log.e("MyViewSimple", "Error loading image: " + e.getMessage());
        }
    }

    /**
     * حفظ الحالة الحالية للتراجع المتدرج
     */
    private void saveCurrentState() {
        try {
            if (workingBitmap != null) {
                // إنشاء نسخة من الحالة الحالية
                Bitmap currentState = workingBitmap.copy(Bitmap.Config.ARGB_8888, false);

                // إضافة الحالة إلى تاريخ التراجع
                undoHistory.add(currentState);
                currentUndoIndex = undoHistory.size() - 1;

                // الحفاظ على الحد الأقصى لخطوات التراجع
                if (undoHistory.size() > maxUndoSteps) {
                    undoHistory.remove(0);
                    currentUndoIndex = undoHistory.size() - 1;
                }

                Log.d("MyViewSimple", "State saved. Undo history size: " + undoHistory.size());
            }
        } catch (Exception e) {
            Log.e("MyViewSimple", "Error saving state: " + e.getMessage());
        }
    }

    /**
     * التراجع المتدرج - خطوة واحدة للخلف
     */
    public void undoMethod() {
        try {
            if (undoHistory.size() > 0 && currentUndoIndex >= 0) {
                // استعادة الحالة السابقة
                Bitmap previousState = undoHistory.get(currentUndoIndex);
                if (previousState != null) {
                    workingBitmap = previousState.copy(Bitmap.Config.ARGB_8888, true);

                    // إزالة الحالة المستعادة من التاريخ
                    undoHistory.remove(currentUndoIndex);
                    currentUndoIndex = undoHistory.size() - 1;

                    invalidate();

                    Log.d("MyViewSimple", "Undo applied. Remaining history: " + undoHistory.size());
                } else {
                    Log.w("MyViewSimple", "Previous state is null");
                }
            } else {
                // إذا لم تعد هناك خطوات للتراجع، العودة للصورة الأصلية
                if (scaledBitmap != null) {
                    workingBitmap = scaledBitmap.copy(Bitmap.Config.ARGB_8888, true);
                    undoHistory.clear();
                    currentUndoIndex = -1;
                    invalidate();

                    Log.d("MyViewSimple", "Reset to original image");
                }
            }
        } catch (Exception e) {
            Log.e("MyViewSimple", "Error in undo: " + e.getMessage());
            // في حالة الخطأ، العودة للصورة الأصلية
            if (scaledBitmap != null) {
                workingBitmap = scaledBitmap.copy(Bitmap.Config.ARGB_8888, true);
                undoHistory.clear();
                currentUndoIndex = -1;
                invalidate();
            }
        }
    }

    /**
     * التحقق من إمكانية التراجع
     */
    public boolean canUndo() {
        return undoHistory.size() > 0 || (scaledBitmap != null && workingBitmap != null);
    }

    /**
     * مسح تاريخ التراجع
     */
    public void clearUndoHistory() {
        try {
            undoHistory.clear();
            currentUndoIndex = -1;
            Log.d("MyViewSimple", "Undo history cleared");
        } catch (Exception e) {
            Log.e("MyViewSimple", "Error clearing undo history: " + e.getMessage());
        }
    }

    /**
     * إعادة تعيين الصورة مع حفظ الحالة للتراجع
     */
    public void resetImageWithUndo() {
        try {
            // حفظ الحالة الحالية قبل الإعادة
            saveCurrentState();

            // إعادة تعيين إلى الصورة الأصلية
            if (scaledBitmap != null) {
                workingBitmap = scaledBitmap.copy(Bitmap.Config.ARGB_8888, true);
                invalidate();

                Log.d("MyViewSimple", "Image reset with undo support");
            }
        } catch (Exception e) {
            Log.e("MyViewSimple", "Error resetting image: " + e.getMessage());
        }
    }
}
