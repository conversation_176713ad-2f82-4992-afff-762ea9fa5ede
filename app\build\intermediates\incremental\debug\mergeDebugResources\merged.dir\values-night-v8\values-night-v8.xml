<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="animals_color">#6BC7E8</color>
    <color name="background_color">#121212</color>
    <color name="border_color">#505050</color>
    <color name="button_background">#3D3D3D</color>
    <color name="button_text">#FFFFFF</color>
    <color name="card_background">#2D2D2D</color>
    <color name="cartoons_color">#6EDDD6</color>
    <color name="divider_color">#404040</color>
    <color name="error_color">#CF6679</color>
    <color name="flowers_color">#FF8FB3</color>
    <color name="foods_color">#FFB74D</color>
    <color name="modern_accent">#FFB74D</color>
    <color name="modern_accent_light">#2D2D2D</color>
    <color name="modern_primary">#8B9DC3</color>
    <color name="nature_color">#C471CE</color>
    <color name="nav_categories_color">#6EDDD6</color>
    <color name="nav_favorites_color">#FF8FB3</color>
    <color name="nav_gallery_color">#C471CE</color>
    <color name="nav_home_color">#8B9DC3</color>
    <color name="nav_more_color">#9CA3AF</color>
    <color name="navigation_bar_color">#121212</color>
    <color name="ripple_color">#40FFFFFF</color>
    <color name="status_bar_color">#0D1117</color>
    <color name="success_color">#81C784</color>
    <color name="surface_color">#1E1E1E</color>
    <color name="text_on_primary">#000000</color>
    <color name="text_primary">#FFFFFF</color>
    <color name="text_secondary">#B3B3B3</color>
    <color name="transport_color">#81C784</color>
    <color name="warning_color">#FFB74D</color>
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.DarkActionBar">
        <item name="colorPrimary">@color/modern_primary</item>
        <item name="colorPrimaryDark">@color/status_bar_color</item>
        <item name="colorAccent">@color/modern_accent</item>
        <item name="android:windowBackground">@color/background_color</item>
        <item name="android:colorBackground">@color/background_color</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="android:statusBarColor">@color/status_bar_color</item>
        <item name="android:navigationBarColor">@color/navigation_bar_color</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
    <style name="DarkBodyText">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/blabeloo</item>
    </style>
    <style name="DarkButtonStyle" parent="Widget.AppCompat.Button">
        <item name="android:background">@color/button_background</item>
        <item name="android:textColor">@color/button_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/blabeloo</item>
    </style>
    <style name="DarkCardStyle" parent="CardView">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardElevation">8dp</item>
        <item name="cardCornerRadius">12dp</item>
    </style>
    <style name="DarkNavigationStyle">
        <item name="android:background">@color/card_background</item>
        <item name="android:elevation">8dp</item>
    </style>
    <style name="DarkTitleText">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/blabeloo</item>
    </style>
    <style name="ModernSwitchStyle" parent="Widget.AppCompat.CompoundButton.Switch">
        <item name="colorControlActivated">@color/modern_accent</item>
        <item name="colorSwitchThumbNormal">@color/text_secondary</item>
        <item name="android:colorForeground">@color/text_primary</item>
    </style>
    <style name="SettingsTheme" parent="AppTheme">
        <item name="android:windowBackground">@color/background_color</item>
        <item name="colorPrimary">@color/modern_primary</item>
        <item name="colorAccent">@color/modern_accent</item>
    </style>
    <style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.DialogWhenLarge"/>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.NoActionBar"/>
    <style name="Theme.Material3.DayNight" parent="Theme.Material3.Dark"/>
    <style name="Theme.Material3.DayNight.BottomSheetDialog" parent="Theme.Material3.Dark.BottomSheetDialog"/>
    <style name="Theme.Material3.DayNight.Dialog" parent="Theme.Material3.Dark.Dialog"/>
    <style name="Theme.Material3.DayNight.Dialog.Alert" parent="Theme.Material3.Dark.Dialog.Alert"/>
    <style name="Theme.Material3.DayNight.Dialog.MinWidth" parent="Theme.Material3.Dark.Dialog.MinWidth"/>
    <style name="Theme.Material3.DayNight.DialogWhenLarge" parent="Theme.Material3.Dark.DialogWhenLarge"/>
    <style name="Theme.Material3.DayNight.NoActionBar" parent="Theme.Material3.Dark.NoActionBar"/>
    <style name="Theme.Material3.DayNight.SideSheetDialog" parent="Theme.Material3.Dark.SideSheetDialog"/>
    <style name="Theme.Material3.DynamicColors.DayNight" parent="Theme.Material3.DynamicColors.Dark"/>
    <style name="Theme.Material3.DynamicColors.DayNight.NoActionBar" parent="Theme.Material3.DynamicColors.Dark.NoActionBar"/>
    <style name="Theme.MaterialComponents.DayNight" parent="Theme.MaterialComponents"/>
    <style name="Theme.MaterialComponents.DayNight.BottomSheetDialog" parent="Theme.MaterialComponents.BottomSheetDialog"/>
    <style name="Theme.MaterialComponents.DayNight.Bridge" parent="Theme.MaterialComponents.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar" parent="Theme.MaterialComponents"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" parent="Theme.MaterialComponents.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog" parent="Theme.MaterialComponents.Dialog"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert" parent="Theme.MaterialComponents.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" parent="Theme.MaterialComponents.Dialog.Alert.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Bridge" parent="Theme.MaterialComponents.Dialog.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" parent="Theme.MaterialComponents.Dialog.FixedSize"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" parent="Theme.MaterialComponents.Dialog.FixedSize.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" parent="Theme.MaterialComponents.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" parent="Theme.MaterialComponents.Dialog.MinWidth.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DialogWhenLarge" parent="Theme.MaterialComponents.DialogWhenLarge"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar" parent="Theme.MaterialComponents.NoActionBar"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" parent="Theme.MaterialComponents.NoActionBar.Bridge"/>
    <style name="ThemeOverlay.AppCompat.DayNight" parent="ThemeOverlay.AppCompat.Dark"/>
    <style name="ThemeOverlay.Material3.DynamicColors.DayNight" parent="ThemeOverlay.Material3.DynamicColors.Dark"/>
    <style name="Widget.MaterialComponents.ActionBar.PrimarySurface" parent="Widget.MaterialComponents.ActionBar.Surface"/>
    <style name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" parent="Widget.MaterialComponents.AppBarLayout.Surface"/>
    <style name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" parent="Widget.MaterialComponents.BottomAppBar"/>
    <style name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" parent="Widget.MaterialComponents.BottomNavigationView"/>
    <style name="Widget.MaterialComponents.NavigationRailView.PrimarySurface" parent="Widget.MaterialComponents.NavigationRailView"/>
    <style name="Widget.MaterialComponents.TabLayout.PrimarySurface" parent="Widget.MaterialComponents.TabLayout"/>
    <style name="Widget.MaterialComponents.Toolbar.PrimarySurface" parent="Widget.MaterialComponents.Toolbar.Surface"/>
</resources>