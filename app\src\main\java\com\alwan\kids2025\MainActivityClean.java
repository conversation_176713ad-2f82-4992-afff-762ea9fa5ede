package com.alwan.kids2025;

import android.Manifest;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.media.MediaPlayer;
import android.media.SoundPool;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

// Google Play Enhanced Features
import com.alwan.kids2025.managers.SimpleReviewManager;

public class MainActivityClean extends AppCompatActivity implements View.OnClickListener {
    private MyViewSimple myView;
    private Paint paint;
    private RelativeLayout drawingLayout;
    private int code = 1;
    private int position = 1;

    // أزرار الألوان
    private ImageButton White, Black, Red, Yellow, LightBlue, DeepPurple, LightOrange, DeepOrange;
    private ImageButton LightPink, LightGreen, DeepPink, DeepGreen, LightPurple, DeepBlue, Brown, Gray;
    private ImageButton SelectColor;

    // Currently selected color button for visual feedback
    private ImageButton currentSelectedColorButton;

    // Color picker dialog
    private android.app.AlertDialog currentColorDialog;

    // Sound and media components
    private MediaPlayer mPlayer;
    private SoundPool soundPool;
    private int paintSoundId, colorSelectSoundId;
    private SharedPreferences preferences;
    private FavoritesManager favoritesManager;
    private ImageView hiddenImageView; // For sharing functionality
    private SimpleReviewManager simpleReviewManager;

    // Permission request codes
    private static final int PERMISSION_REQUEST_WRITE_STORAGE = 1;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main_clean);

        // Setup toolbar
        androidx.appcompat.widget.Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("تلوين");
        }

        // استخراج البيانات الممررة مع معالجة محسنة للأخطاء
        Bundle extras = getIntent().getExtras();
        if (extras != null) {
            code = extras.getInt("code", 1);
            position = extras.getInt("position", 1);

            Log.d("MainActivityClean", "Received from intent - code: " + code + ", position: " + position);

            // التحقق من صحة البيانات
            if (code <= 0 || position <= 0) {
                Log.w("MainActivityClean", "Invalid code or position received, using defaults");
                code = 1;
                position = 1;
            }
        } else {
            Log.d("MainActivityClean", "No extras found, using default values");
        }

        // تهيئة Paint
        initializePaint();

        // تهيئة المكونات
        drawingLayout = findViewById(R.id.relative_layout);

        // إنشاء MyView
        myView = new MyViewSimple(this);

        // تمرير معلومات الصورة إلى MyView
        myView.loadImageByCodeAndPosition(code, position);

        drawingLayout.addView(myView);

        // تهيئة الأزرار
        initializeButtons();

        // تهيئة Paint
        initializePaint();

        // تهيئة المكونات الإضافية
        initializeComponents();

        // ربط مستمع أحداث التلوين
        myView.setOnPaintListener(new MyViewSimple.OnPaintListener() {
            @Override
            public void onPaintApplied() {
                playPaintSound();
            }
        });
    }

    /**
     * تهيئة Paint للرسم
     */
    private void initializePaint() {
        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setDither(true);
        paint.setColor(Color.BLACK);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeJoin(Paint.Join.ROUND);
        paint.setStrokeCap(Paint.Cap.ROUND);
        paint.setStrokeWidth(15);

        // تمرير Paint إلى MyView
        updateMyViewPaint();
    }

    /**
     * تهيئة الأزرار
     */
    private void initializeButtons() {
        try {
            // الصف الأول من الألوان
            DeepOrange = findViewById(R.id.deep_orange);
            LightPink = findViewById(R.id.light_pink);
            LightGreen = findViewById(R.id.light_green);
            Yellow = findViewById(R.id.yellow);
            LightBlue = findViewById(R.id.light_blue);
            DeepPurple = findViewById(R.id.deep_purple);
            LightOrange = findViewById(R.id.light_orange);
            White = findViewById(R.id.white);

            // الصف الثاني من الألوان
            Red = findViewById(R.id.red);
            DeepPink = findViewById(R.id.deep_pink);
            DeepGreen = findViewById(R.id.deep_green);
            LightPurple = findViewById(R.id.light_purple);
            DeepBlue = findViewById(R.id.deep_blue);
            Brown = findViewById(R.id.brown);
            Gray = findViewById(R.id.gray);
            Black = findViewById(R.id.black);

            // زر اختيار اللون
            SelectColor = findViewById(R.id.select_color);

            // تعيين المستمعين للصف الأول
            if (DeepOrange != null) DeepOrange.setOnClickListener(this);
            if (LightPink != null) LightPink.setOnClickListener(this);
            if (LightGreen != null) LightGreen.setOnClickListener(this);
            if (Yellow != null) Yellow.setOnClickListener(this);
            if (LightBlue != null) LightBlue.setOnClickListener(this);
            if (DeepPurple != null) DeepPurple.setOnClickListener(this);
            if (LightOrange != null) LightOrange.setOnClickListener(this);
            if (White != null) White.setOnClickListener(this);

            // تعيين المستمعين للصف الثاني
            if (Red != null) Red.setOnClickListener(this);
            if (DeepPink != null) DeepPink.setOnClickListener(this);
            if (DeepGreen != null) DeepGreen.setOnClickListener(this);
            if (LightPurple != null) LightPurple.setOnClickListener(this);
            if (DeepBlue != null) DeepBlue.setOnClickListener(this);
            if (Brown != null) Brown.setOnClickListener(this);
            if (Gray != null) Gray.setOnClickListener(this);
            if (Black != null) Black.setOnClickListener(this);

            // زر اختيار اللون
            if (SelectColor != null) SelectColor.setOnClickListener(this);

        } catch (Exception e) {
            Log.e("MainActivityClean", "Error initializing buttons: " + e.getMessage());
        }
    }

    /**
     * تحديث Paint في MyView
     */
    private void updateMyViewPaint() {
        if (myView != null && paint != null) {
            myView.updatePaint(paint);
        }
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();

        // الصف الأول من الألوان
        if (id == R.id.deep_orange) {
            paint.setColor(Color.parseColor("#FF5722")); // Deep Orange
        } else if (id == R.id.light_pink) {
            paint.setColor(Color.parseColor("#F8BBD9")); // Light Pink
        } else if (id == R.id.light_green) {
            paint.setColor(Color.parseColor("#8BC34A")); // Light Green
        } else if (id == R.id.yellow) {
            paint.setColor(Color.YELLOW);
        } else if (id == R.id.light_blue) {
            paint.setColor(Color.parseColor("#03A9F4")); // Light Blue
        } else if (id == R.id.deep_purple) {
            paint.setColor(Color.parseColor("#673AB7")); // Deep Purple
        } else if (id == R.id.light_orange) {
            paint.setColor(Color.parseColor("#FF9800")); // Light Orange
        } else if (id == R.id.white) {
            paint.setColor(Color.WHITE);
        }
        // الصف الثاني من الألوان
        else if (id == R.id.red) {
            paint.setColor(Color.RED);
        } else if (id == R.id.deep_pink) {
            paint.setColor(Color.parseColor("#E91E63")); // Deep Pink
        } else if (id == R.id.deep_green) {
            paint.setColor(Color.parseColor("#4CAF50")); // Deep Green
        } else if (id == R.id.light_purple) {
            paint.setColor(Color.parseColor("#9C27B0")); // Light Purple
        } else if (id == R.id.deep_blue) {
            paint.setColor(Color.parseColor("#2196F3")); // Deep Blue
        } else if (id == R.id.brown) {
            paint.setColor(Color.parseColor("#795548")); // Brown
        } else if (id == R.id.gray) {
            paint.setColor(Color.GRAY);
        } else if (id == R.id.black) {
            paint.setColor(Color.BLACK);
        } else if (id == R.id.select_color) {
            // فتح منتقي الألوان
            showColorPicker();
            return; // لا نحتاج لتحديث Paint هنا
        }

        updateMyViewPaint();

        // تحديث التغذية البصرية للون المحدد
        updateSelectedColorFeedback((ImageButton) view);

        // تشغيل صوت اختيار اللون
        playColorSelectSound();
    }

    /**
     * تهيئة المكونات الإضافية
     */
    private void initializeComponents() {
        // تهيئة SharedPreferences
        preferences = getSharedPreferences("app_settings", MODE_PRIVATE);

        // تهيئة FavoritesManager
        favoritesManager = new FavoritesManager(this);

        // تهيئة SoundPool
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            soundPool = new SoundPool.Builder()
                .setMaxStreams(5)
                .build();
        } else {
            soundPool = new SoundPool(5, android.media.AudioManager.STREAM_MUSIC, 0);
        }

        // تحميل الأصوات
        loadSounds();

        // تهيئة MediaPlayer للموسيقى الخلفية
        initializeBackgroundMusic();

        // إنشاء ImageView مخفي للمشاركة
        hiddenImageView = new ImageView(this);
    }

    /**
     * تحميل ملفات الصوت
     */
    private void loadSounds() {
        try {
            // تحميل صوت النقر (للرسم واختيار الألوان) باستخدام R.raw مباشرة
            paintSoundId = soundPool.load(this, R.raw.click, 1);
            colorSelectSoundId = soundPool.load(this, R.raw.click, 1);
            Log.d("MainActivityClean", "Sound files loaded successfully");
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error loading sounds: " + e.getMessage());
        }
    }

    /**
     * تهيئة الموسيقى الخلفية
     */
    private void initializeBackgroundMusic() {
        try {
            boolean backgroundMusicEnabled = preferences.getBoolean("background_music", true);
            if (backgroundMusicEnabled) {
                // استخدام R.raw مباشرة بدلاً من getIdentifier
                mPlayer = MediaPlayer.create(this, R.raw.background_1);
                if (mPlayer != null) {
                    mPlayer.setLooping(true);
                    mPlayer.setVolume(0.5f, 0.5f); // تقليل مستوى الصوت
                    mPlayer.start();
                    Log.d("MainActivityClean", "Background music started successfully");
                } else {
                    Log.w("MainActivityClean", "Failed to create MediaPlayer");
                }
            } else {
                Log.d("MainActivityClean", "Background music disabled in preferences");
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error initializing background music: " + e.getMessage());
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.share_save_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();

        switch (id) {
            case android.R.id.home:
                // Back button pressed
                onBackPressed();
                return true;

            case R.id.action_share:
                shareImage();
                return true;

            case R.id.action_save:
                saveImage();
                return true;

            case R.id.action_mute:
                toggleSound();
                return true;

            case R.id.action_undo:
                undoLastAction();
                return true;

            case R.id.action_favorite:
                toggleFavorite();
                return true;

            default:
                return super.onOptionsItemSelected(item);
        }
    }

    /**
     * مشاركة الصورة
     */
    private void shareImage() {
        try {
            if (myView != null && myView.workingBitmap != null) {
                Uri bmpUri = getLocalBitmapUri(myView.workingBitmap);
                if (bmpUri != null) {
                    Intent shareIntent = new Intent();
                    shareIntent.setAction(Intent.ACTION_SEND);
                    shareIntent.putExtra(Intent.EXTRA_STREAM, bmpUri);
                    shareIntent.putExtra(Intent.EXTRA_TEXT,
                        "شفتوا إبداعي؟!\nأنا رسمت هذه الرسمة باستخدام تطبيق ألوان الأطفال! 🎨💛\n" +
                        "فيه أدوات ممتعة وألوان جميلة تخليك ترسم وتلون بطريقتك الخاصة!\n" +
                        "حمّل التطبيق وورينا إبداعك 👇\n" +
                        "https://play.google.com/store/apps/details?id=" + getPackageName());
                    shareIntent.setType("image/*");
                    shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    startActivity(Intent.createChooser(shareIntent, "مشاركة الصورة"));
                    Toast.makeText(this, "تم فتح نافذة المشاركة", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(this, "خطأ في إنشاء الصورة للمشاركة", Toast.LENGTH_SHORT).show();
                }
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error sharing image: " + e.getMessage());
            Toast.makeText(this, "خطأ في مشاركة الصورة", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * إنشاء URI للصورة للمشاركة
     */
    private Uri getLocalBitmapUri(Bitmap bitmap) {
        try {
            // إنشاء ملف مؤقت للمشاركة
            File cachePath = new File(getCacheDir(), "images");
            cachePath.mkdirs();
            File file = new File(cachePath, "shared_image_" + System.currentTimeMillis() + ".png");

            FileOutputStream out = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 90, out);
            out.close();

            // استخدام FileProvider للحصول على URI آمن
            return FileProvider.getUriForFile(this, getPackageName() + ".fileprovider", file);
        } catch (IOException e) {
            Log.e("MainActivityClean", "Error creating bitmap URI: " + e.getMessage());
            return null;
        }
    }

    /**
     * حفظ الصورة
     */
    private void saveImage() {
        try {
            if (myView != null && myView.workingBitmap != null) {
                // التحقق من الصلاحيات
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    // Android 10+ - استخدام MediaStore
                    saveImageToMediaStore(myView.workingBitmap);
                } else {
                    // Android 9 وأقل - التحقق من صلاحية الكتابة
                    if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                            != PackageManager.PERMISSION_GRANTED) {
                        ActivityCompat.requestPermissions(this,
                            new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE},
                            PERMISSION_REQUEST_WRITE_STORAGE);
                        return;
                    }
                    saveImageToExternalStorage(myView.workingBitmap);
                }
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error saving image: " + e.getMessage());
            Toast.makeText(this, "خطأ في حفظ الصورة", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * حفظ الصورة باستخدام MediaStore (Android 10+)
     */
    private void saveImageToMediaStore(Bitmap bitmap) {
        try {
            String fileName = "colored_image_" + System.currentTimeMillis() + ".png";

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                android.content.ContentValues values = new android.content.ContentValues();
                values.put(MediaStore.Images.Media.DISPLAY_NAME, fileName);
                values.put(MediaStore.Images.Media.MIME_TYPE, "image/png");
                values.put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES + "/ألوان الأطفال");

                Uri uri = getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
                if (uri != null) {
                    try (java.io.OutputStream out = getContentResolver().openOutputStream(uri)) {
                        bitmap.compress(Bitmap.CompressFormat.PNG, 90, out);
                        Toast.makeText(this, "تم حفظ الصورة في المعرض", Toast.LENGTH_SHORT).show();
                    }
                }
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error saving to MediaStore: " + e.getMessage());
            Toast.makeText(this, "خطأ في حفظ الصورة", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * حفظ الصورة في التخزين الخارجي (Android 9 وأقل)
     */
    private void saveImageToExternalStorage(Bitmap bitmap) {
        try {
            File dir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
                "ألوان الأطفال");
            if (!dir.exists()) {
                dir.mkdirs();
            }

            String fileName = "colored_image_" + System.currentTimeMillis() + ".png";
            File file = new File(dir, fileName);

            FileOutputStream fos = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 90, fos);
            fos.flush();
            fos.close();

            // إضافة الصورة إلى معرض الصور
            MediaStore.Images.Media.insertImage(getContentResolver(), file.getAbsolutePath(), fileName, null);

            Toast.makeText(this, "تم حفظ الصورة في: " + file.getAbsolutePath(), Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error saving to external storage: " + e.getMessage());
            Toast.makeText(this, "خطأ في حفظ الصورة", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_WRITE_STORAGE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // تم منح الصلاحية، حاول الحفظ مرة أخرى
                saveImage();
            } else {
                Toast.makeText(this, "يجب منح صلاحية الكتابة لحفظ الصورة", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * تبديل الصوت
     */
    private void toggleSound() {
        try {
            boolean soundEnabled = preferences.getBoolean("sound_effects", true);
            boolean backgroundMusicEnabled = preferences.getBoolean("background_music", true);

            if (soundEnabled || backgroundMusicEnabled) {
                // إيقاف الصوت
                preferences.edit()
                    .putBoolean("sound_effects", false)
                    .putBoolean("background_music", false)
                    .apply();

                // إيقاف الموسيقى الخلفية
                if (mPlayer != null && mPlayer.isPlaying()) {
                    mPlayer.pause();
                }

                Toast.makeText(this, "تم إيقاف الصوت", Toast.LENGTH_SHORT).show();
            } else {
                // تشغيل الصوت
                preferences.edit()
                    .putBoolean("sound_effects", true)
                    .putBoolean("background_music", true)
                    .apply();

                // تشغيل الموسيقى الخلفية
                if (mPlayer != null && !mPlayer.isPlaying()) {
                    mPlayer.start();
                }

                Toast.makeText(this, "تم تشغيل الصوت", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error toggling sound: " + e.getMessage());
        }
    }

    /**
     * تشغيل صوت التلوين
     */
    private void playPaintSound() {
        try {
            boolean soundEnabled = preferences.getBoolean("sound_effects", true);
            if (soundEnabled && soundPool != null && paintSoundId != 0) {
                soundPool.play(paintSoundId, 1.0f, 1.0f, 1, 0, 1.0f);
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error playing paint sound: " + e.getMessage());
        }
    }

    /**
     * تشغيل صوت اختيار اللون
     */
    private void playColorSelectSound() {
        try {
            boolean soundEnabled = preferences.getBoolean("sound_effects", true);
            if (soundEnabled && soundPool != null && colorSelectSoundId != 0) {
                soundPool.play(colorSelectSoundId, 1.0f, 1.0f, 1, 0, 1.0f);
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error playing color select sound: " + e.getMessage());
        }
    }

    /**
     * عرض منتقي الألوان المتقدم - HSV Color Picker
     */
    private void showColorPicker() {
        try {
            // إنشاء HSV Color Picker متقدم
            createAdvancedHSVColorPicker();

        } catch (Exception e) {
            // في حالة الخطأ، استخدم لون افتراضي
            paint.setColor(android.graphics.Color.parseColor("#FF6B9D"));
            updateMyViewPaint();
            updateSelectedColorFeedback(SelectColor);
            playColorSelectSound();
        }
    }

    /**
     * إنشاء Color Picker مخصص للأطفال بتصميم جذاب
     */
    private void createAdvancedHSVColorPicker() {
        try {
            // إنشاء Color Picker مخصص للأطفال
            createKidsColorPicker();

        } catch (Exception e) {
            // في حالة الخطأ، استخدم Color Picker البديل
            createFallbackColorPicker();
        }
    }

    /**
     * إنشاء Color Picker مخصص للأطفال مع تصميم جذاب
     */
    private void createKidsColorPicker() {
        try {
            // إنشاء Dialog مخصص للأطفال
            android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);

            // إنشاء Layout مخصص
            android.widget.LinearLayout mainLayout = new android.widget.LinearLayout(this);
            mainLayout.setOrientation(android.widget.LinearLayout.VERTICAL);
            mainLayout.setPadding(30, 30, 30, 30);

            // إضافة خلفية متدرجة جميلة
            android.graphics.drawable.GradientDrawable backgroundGradient =
                new android.graphics.drawable.GradientDrawable();
            backgroundGradient.setColors(new int[]{
                android.graphics.Color.parseColor("#FFE5F3"), // وردي فاتح
                android.graphics.Color.parseColor("#E8F4FD"), // أزرق فاتح
                android.graphics.Color.parseColor("#FFF9E5")  // أصفر فاتح
            });
            backgroundGradient.setOrientation(android.graphics.drawable.GradientDrawable.Orientation.TOP_BOTTOM);
            backgroundGradient.setCornerRadius(25);
            mainLayout.setBackground(backgroundGradient);

            // إضافة عنوان جذاب
            android.widget.TextView titleText = new android.widget.TextView(this);
            titleText.setText("🌈 اختر لونك المفضل! 🎨");
            titleText.setTextSize(24);
            titleText.setTextColor(android.graphics.Color.parseColor("#FF6B9D"));
            titleText.setGravity(android.view.Gravity.CENTER);
            titleText.setPadding(0, 0, 0, 30);
            titleText.setTypeface(null, android.graphics.Typeface.BOLD);
            mainLayout.addView(titleText);

            // إنشاء ألوان خاصة للأطفال
            createKidsColorPalette(mainLayout);

            // إضافة أزرار التحكم
            createKidsControlButtons(mainLayout, builder);

            builder.setView(mainLayout);

            // إنشاء وعرض Dialog
            currentColorDialog = builder.create();
            currentColorDialog.show();

            // تخصيص حجم Dialog
            android.view.Window window = currentColorDialog.getWindow();
            if (window != null) {
                window.setLayout(1100, 900);
                // إضافة خلفية شفافة
                window.setBackgroundDrawableResource(android.R.color.transparent);
            }

        } catch (Exception e) {
            // في حالة الخطأ، استخدم Color Picker البديل
            createFallbackColorPicker();
        }
    }

    /**
     * إنشاء زر لون مخصص للأطفال مع تأثيرات جميلة
     */
    private android.widget.Button createKidsColorButton(int color) {
        try {
            // إنشاء زر مخصص
            android.widget.Button colorButton = new android.widget.Button(this);

            // إعداد حجم الزر (أكبر للأطفال)
            android.widget.LinearLayout.LayoutParams buttonParams =
                new android.widget.LinearLayout.LayoutParams(120, 120);
            buttonParams.setMargins(8, 8, 8, 8);
            colorButton.setLayoutParams(buttonParams);

            // إنشاء خلفية متدرجة جميلة
            android.graphics.drawable.GradientDrawable buttonBackground =
                new android.graphics.drawable.GradientDrawable();

            // إضافة تدرج لوني جميل
            int lighterColor = lightenColor(color, 0.3f);
            buttonBackground.setColors(new int[]{lighterColor, color});
            buttonBackground.setOrientation(android.graphics.drawable.GradientDrawable.Orientation.TOP_BOTTOM);

            // إضافة حدود ملونة وزوايا مدورة
            buttonBackground.setStroke(6, android.graphics.Color.WHITE);
            buttonBackground.setCornerRadius(20);

            // إضافة ظل جميل
            colorButton.setElevation(8);

            colorButton.setBackground(buttonBackground);

            // إضافة مستمع النقر مع تأثيرات
            colorButton.setOnClickListener(new android.view.View.OnClickListener() {
                @Override
                public void onClick(android.view.View v) {
                    // تأثير انيميشن عند الضغط
                    animateButtonPress(v);

                    // تطبيق اللون المختار
                    paint.setColor(color);
                    updateMyViewPaint();
                    updateSelectedColorFeedback(SelectColor);
                    playColorSelectSound();

                    // إغلاق Dialog مع تأخير بسيط للانيميشن
                    v.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (currentColorDialog != null) {
                                currentColorDialog.dismiss();
                            }
                        }
                    }, 200);
                }
            });

            return colorButton;

        } catch (Exception e) {
            // في حالة الخطأ، أنشئ زر بسيط
            android.widget.Button simpleButton = new android.widget.Button(this);
            simpleButton.setBackgroundColor(color);
            return simpleButton;
        }
    }

    /**
     * تفتيح لون معين
     */
    private int lightenColor(int color, float factor) {
        try {
            int red = android.graphics.Color.red(color);
            int green = android.graphics.Color.green(color);
            int blue = android.graphics.Color.blue(color);

            red = (int) (red + (255 - red) * factor);
            green = (int) (green + (255 - green) * factor);
            blue = (int) (blue + (255 - blue) * factor);

            return android.graphics.Color.rgb(red, green, blue);
        } catch (Exception e) {
            return color;
        }
    }

    /**
     * انيميشن ضغط الزر
     */
    private void animateButtonPress(android.view.View button) {
        try {
            // انيميشن تصغير وتكبير
            button.animate()
                .scaleX(0.9f)
                .scaleY(0.9f)
                .setDuration(100)
                .withEndAction(new Runnable() {
                    @Override
                    public void run() {
                        button.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(100)
                            .start();
                    }
                })
                .start();
        } catch (Exception e) {
            // في حالة الخطأ، لا تفعل شيء
        }
    }

    /**
     * إنشاء أزرار التحكم للأطفال
     */
    private void createKidsControlButtons(android.widget.LinearLayout parentLayout,
                                        android.app.AlertDialog.Builder builder) {
        try {
            // إنشاء layout للأزرار
            android.widget.LinearLayout buttonsLayout = new android.widget.LinearLayout(this);
            buttonsLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);
            buttonsLayout.setGravity(android.view.Gravity.CENTER);
            buttonsLayout.setPadding(0, 30, 0, 0);

            // زر إغلاق جميل
            android.widget.Button closeButton = createKidsControlButton("إغلاق 🚪", "#FF6B9D");
            closeButton.setOnClickListener(new android.view.View.OnClickListener() {
                @Override
                public void onClick(android.view.View v) {
                    animateButtonPress(v);
                    if (currentColorDialog != null) {
                        currentColorDialog.dismiss();
                    }
                }
            });

            // زر إعادة تعيين جميل
            android.widget.Button resetButton = createKidsControlButton("إعادة تعيين 🔄", "#4ECDC4");
            resetButton.setOnClickListener(new android.view.View.OnClickListener() {
                @Override
                public void onClick(android.view.View v) {
                    animateButtonPress(v);
                    paint.setColor(android.graphics.Color.parseColor("#FF6B9D"));
                    updateMyViewPaint();
                    updateSelectedColorFeedback(SelectColor);
                    playColorSelectSound();
                    if (currentColorDialog != null) {
                        currentColorDialog.dismiss();
                    }
                }
            });

            buttonsLayout.addView(closeButton);
            buttonsLayout.addView(resetButton);
            parentLayout.addView(buttonsLayout);

        } catch (Exception e) {
            // في حالة الخطأ، لا تفعل شيء
        }
    }

    /**
     * إنشاء زر تحكم مخصص للأطفال
     */
    private android.widget.Button createKidsControlButton(String text, String colorHex) {
        try {
            android.widget.Button button = new android.widget.Button(this);
            button.setText(text);
            button.setTextSize(16);
            button.setTextColor(android.graphics.Color.WHITE);
            button.setTypeface(null, android.graphics.Typeface.BOLD);

            // إعداد حجم الزر
            android.widget.LinearLayout.LayoutParams buttonParams =
                new android.widget.LinearLayout.LayoutParams(200, 80);
            buttonParams.setMargins(15, 0, 15, 0);
            button.setLayoutParams(buttonParams);

            // إنشاء خلفية متدرجة
            android.graphics.drawable.GradientDrawable buttonBackground =
                new android.graphics.drawable.GradientDrawable();

            int color = android.graphics.Color.parseColor(colorHex);
            int lighterColor = lightenColor(color, 0.3f);
            buttonBackground.setColors(new int[]{lighterColor, color});
            buttonBackground.setOrientation(android.graphics.drawable.GradientDrawable.Orientation.TOP_BOTTOM);
            buttonBackground.setCornerRadius(25);
            buttonBackground.setStroke(3, android.graphics.Color.WHITE);

            button.setBackground(buttonBackground);
            button.setElevation(6);

            return button;

        } catch (Exception e) {
            // في حالة الخطأ، أنشئ زر بسيط
            android.widget.Button simpleButton = new android.widget.Button(this);
            simpleButton.setText(text);
            return simpleButton;
        }
    }

    /**
     * إنشاء لوحة ألوان مخصصة للأطفال
     */
    private void createKidsColorPalette(android.widget.LinearLayout parentLayout) {
        try {
            // ألوان قوس قزح للأطفال
            int[][] kidsColors = {
                // الصف الأول - ألوان قوس قزح الزاهية
                {
                    android.graphics.Color.parseColor("#FF0000"), // أحمر زاهي
                    android.graphics.Color.parseColor("#FF8C00"), // برتقالي زاهي
                    android.graphics.Color.parseColor("#FFD700"), // أصفر ذهبي
                    android.graphics.Color.parseColor("#32CD32"), // أخضر ليموني
                    android.graphics.Color.parseColor("#00BFFF"), // أزرق سماوي
                    android.graphics.Color.parseColor("#8A2BE2"), // بنفسجي أزرق
                    android.graphics.Color.parseColor("#FF69B4")  // وردي زاهي
                },
                // الصف الثاني - ألوان الحلوى
                {
                    android.graphics.Color.parseColor("#FFB6C1"), // وردي حلوى
                    android.graphics.Color.parseColor("#FFCCCB"), // وردي مشمشي
                    android.graphics.Color.parseColor("#FFEAA7"), // أصفر كريمي
                    android.graphics.Color.parseColor("#98FB98"), // أخضر نعناعي
                    android.graphics.Color.parseColor("#87CEEB"), // أزرق سماوي فاتح
                    android.graphics.Color.parseColor("#DDA0DD"), // بنفسجي فاتح
                    android.graphics.Color.parseColor("#F0E68C")  // خاكي فاتح
                },
                // الصف الثالث - ألوان الطبيعة
                {
                    android.graphics.Color.parseColor("#228B22"), // أخضر غابة
                    android.graphics.Color.parseColor("#4169E1"), // أزرق ملكي
                    android.graphics.Color.parseColor("#DC143C"), // أحمر قرمزي
                    android.graphics.Color.parseColor("#FF4500"), // أحمر برتقالي
                    android.graphics.Color.parseColor("#9370DB"), // بنفسجي متوسط
                    android.graphics.Color.parseColor("#20B2AA"), // تركوازي
                    android.graphics.Color.parseColor("#CD853F")  // بيرو
                },
                // الصف الرابع - ألوان إضافية
                {
                    android.graphics.Color.parseColor("#000000"), // أسود
                    android.graphics.Color.parseColor("#FFFFFF"), // أبيض
                    android.graphics.Color.parseColor("#808080"), // رمادي
                    android.graphics.Color.parseColor("#A52A2A"), // بني
                    android.graphics.Color.parseColor("#800080"), // بنفسجي
                    android.graphics.Color.parseColor("#008080"), // تيل
                    android.graphics.Color.parseColor("#FFA500")  // برتقالي
                }
            };

            // إنشاء صفوف الألوان
            for (int row = 0; row < kidsColors.length; row++) {
                android.widget.LinearLayout rowLayout = new android.widget.LinearLayout(this);
                rowLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);
                rowLayout.setGravity(android.view.Gravity.CENTER);
                rowLayout.setPadding(0, 10, 0, 10);

                for (int col = 0; col < kidsColors[row].length; col++) {
                    final int color = kidsColors[row][col];

                    // إنشاء زر لون مخصص للأطفال
                    android.widget.Button colorButton = createKidsColorButton(color);
                    rowLayout.addView(colorButton);
                }

                parentLayout.addView(rowLayout);
            }

        } catch (Exception e) {
            // في حالة الخطأ، لا تفعل شيء
        }
    }

    /**
     * Color Picker بديل في حالة فشل المكتبة المتقدمة
     */
    private void createFallbackColorPicker() {
        try {
            // ألوان أساسية للاختيار السريع
            int[] fallbackColors = {
                android.graphics.Color.parseColor("#FF0000"), // أحمر
                android.graphics.Color.parseColor("#00FF00"), // أخضر
                android.graphics.Color.parseColor("#0000FF"), // أزرق
                android.graphics.Color.parseColor("#FFFF00"), // أصفر
                android.graphics.Color.parseColor("#FF00FF"), // ماجنتا
                android.graphics.Color.parseColor("#00FFFF"), // سيان
                android.graphics.Color.parseColor("#FFA500"), // برتقالي
                android.graphics.Color.parseColor("#800080"), // بنفسجي
                android.graphics.Color.parseColor("#FF6B9D"), // وردي جميل
                android.graphics.Color.parseColor("#000000")  // أسود
            };

            // إنشاء Dialog بسيط
            android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
            builder.setTitle("🎨 اختر لوناً");

            String[] colorNames = {
                "🔴 أحمر", "🟢 أخضر", "🔵 أزرق", "🟡 أصفر", "🟣 ماجنتا",
                "🔵 سيان", "🟠 برتقالي", "🟣 بنفسجي", "💖 وردي جميل", "⚫ أسود"
            };

            builder.setItems(colorNames, new android.content.DialogInterface.OnClickListener() {
                @Override
                public void onClick(android.content.DialogInterface dialog, int which) {
                    paint.setColor(fallbackColors[which]);
                    updateMyViewPaint();
                    updateSelectedColorFeedback(SelectColor);
                    playColorSelectSound();
                    dialog.dismiss();
                }
            });

            builder.setNegativeButton("إلغاء", null);
            currentColorDialog = builder.create();
            currentColorDialog.show();

        } catch (Exception e) {
            // في حالة الفشل التام، استخدم لون افتراضي
            paint.setColor(android.graphics.Color.parseColor("#FF6B9D"));
            updateMyViewPaint();
            updateSelectedColorFeedback(SelectColor);
            playColorSelectSound();
        }
    }

    /**
     * إنشاء Color Picker Dialog احترافي بشبكة ألوان مرئية
     */
    private void createProfessionalColorPickerDialog(int[] colors) {
        try {
            // إنشاء Dialog احترافي
            android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
            builder.setTitle("🎨 اختر لوناً للتلوين");

            // إنشاء ScrollView للتمرير
            android.widget.ScrollView scrollView = new android.widget.ScrollView(this);

            // إنشاء LinearLayout عمودي
            android.widget.LinearLayout mainLayout = new android.widget.LinearLayout(this);
            mainLayout.setOrientation(android.widget.LinearLayout.VERTICAL);
            mainLayout.setPadding(20, 20, 20, 20);

            // إنشاء شبكة الألوان (10 ألوان في كل صف)
            int colorsPerRow = 10;
            for (int row = 0; row < (colors.length + colorsPerRow - 1) / colorsPerRow; row++) {
                android.widget.LinearLayout rowLayout = new android.widget.LinearLayout(this);
                rowLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);
                rowLayout.setGravity(android.view.Gravity.CENTER);

                for (int col = 0; col < colorsPerRow; col++) {
                    int index = row * colorsPerRow + col;
                    if (index >= colors.length) break;

                    final int color = colors[index];

                    // إنشاء زر للون
                    android.widget.Button colorButton = new android.widget.Button(this);

                    // إعداد حجم الزر
                    android.widget.LinearLayout.LayoutParams buttonParams =
                        new android.widget.LinearLayout.LayoutParams(80, 80);
                    buttonParams.setMargins(5, 5, 5, 5);
                    colorButton.setLayoutParams(buttonParams);

                    // تعيين لون الخلفية
                    colorButton.setBackgroundColor(color);

                    // إضافة حدود بيضاء
                    android.graphics.drawable.GradientDrawable drawable = new android.graphics.drawable.GradientDrawable();
                    drawable.setColor(color);
                    drawable.setStroke(3, android.graphics.Color.WHITE);
                    drawable.setCornerRadius(10);
                    colorButton.setBackground(drawable);

                    // إضافة مستمع النقر
                    colorButton.setOnClickListener(new android.view.View.OnClickListener() {
                        @Override
                        public void onClick(android.view.View v) {
                            // تطبيق اللون المختار
                            paint.setColor(color);
                            updateMyViewPaint();
                            updateSelectedColorFeedback(SelectColor);
                            playColorSelectSound();

                            // إغلاق Dialog
                            if (currentColorDialog != null) {
                                currentColorDialog.dismiss();
                            }
                        }
                    });

                    rowLayout.addView(colorButton);
                }

                mainLayout.addView(rowLayout);
            }

            scrollView.addView(mainLayout);
            builder.setView(scrollView);

            // إضافة زر إلغاء
            builder.setNegativeButton("إلغاء", new android.content.DialogInterface.OnClickListener() {
                @Override
                public void onClick(android.content.DialogInterface dialog, int which) {
                    dialog.dismiss();
                }
            });

            // إنشاء وعرض Dialog
            currentColorDialog = builder.create();
            currentColorDialog.show();

            // تخصيص حجم Dialog
            android.view.Window window = currentColorDialog.getWindow();
            if (window != null) {
                window.setLayout(900, 700);
            }

        } catch (Exception e) {
            // في حالة الخطأ، استخدم لون افتراضي
            paint.setColor(android.graphics.Color.parseColor("#FF6B9D"));
            updateMyViewPaint();
            updateSelectedColorFeedback(SelectColor);
            playColorSelectSound();
        }
    }

    /**
     * إنشاء Color Picker Dialog مبسط جداً
     */
    private void createSimpleColorPickerDialog(int[] colors) {
        try {
            // إنشاء Dialog بسيط
            android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
            builder.setTitle("🎨 اختر لوناً");

            // إنشاء قائمة بأسماء الألوان
            String[] colorNames = {
                "🔴 أحمر", "🟢 أخضر", "🔵 أزرق", "🟡 أصفر", "🟣 ماجنتا",
                "🔵 سيان", "🟠 برتقالي", "🟣 بنفسجي", "🌸 وردي", "🟤 بني",
                "💖 وردي جميل", "🌊 تركوازي", "💙 أزرق فاتح", "🌿 أخضر نعناعي", "🌟 أصفر كريمي",
                "💜 بنفسجي فاتح", "🧡 مرجاني", "🌀 أزرق مخضر", "⚫ أسود", "⚪ أبيض"
            };

            builder.setItems(colorNames, new android.content.DialogInterface.OnClickListener() {
                @Override
                public void onClick(android.content.DialogInterface dialog, int which) {
                    // تطبيق اللون المختار
                    paint.setColor(colors[which]);
                    updateMyViewPaint();
                    updateSelectedColorFeedback(SelectColor);
                    playColorSelectSound();
                    dialog.dismiss();
                }
            });

            builder.setNegativeButton("إلغاء", null);

            // إنشاء وعرض Dialog
            currentColorDialog = builder.create();
            currentColorDialog.show();

        } catch (Exception e) {
            // في حالة الخطأ، استخدم لون افتراضي
            paint.setColor(android.graphics.Color.parseColor("#FF6B9D"));
            updateMyViewPaint();
            updateSelectedColorFeedback(SelectColor);
            playColorSelectSound();
        }
    }

    /**
     * إنشاء Color Picker Dialog مبسط وفعال
     */
    private void createColorPickerDialog(int[] colors) {
        try {
            // إنشاء Dialog بسيط
            android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
            builder.setTitle("🎨 اختر لوناً للتلوين");

            // إنشاء LinearLayout عمودي
            android.widget.LinearLayout mainLayout = new android.widget.LinearLayout(this);
            mainLayout.setOrientation(android.widget.LinearLayout.VERTICAL);
            mainLayout.setPadding(30, 30, 30, 30);

            // إنشاء صفوف من الألوان
            int colorsPerRow = 5;
            for (int row = 0; row < (colors.length + colorsPerRow - 1) / colorsPerRow; row++) {
                android.widget.LinearLayout rowLayout = new android.widget.LinearLayout(this);
                rowLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);
                rowLayout.setGravity(android.view.Gravity.CENTER);

                for (int col = 0; col < colorsPerRow; col++) {
                    int index = row * colorsPerRow + col;
                    if (index >= colors.length) break;

                    final int color = colors[index];

                    // إنشاء زر للون
                    android.widget.Button colorButton = new android.widget.Button(this);

                    // إعداد حجم الزر
                    android.widget.LinearLayout.LayoutParams buttonParams =
                        new android.widget.LinearLayout.LayoutParams(100, 100);
                    buttonParams.setMargins(10, 10, 10, 10);
                    colorButton.setLayoutParams(buttonParams);

                    // تعيين لون الخلفية
                    colorButton.setBackgroundColor(color);

                    // إضافة مستمع النقر
                    colorButton.setOnClickListener(new android.view.View.OnClickListener() {
                        @Override
                        public void onClick(android.view.View v) {
                            // تطبيق اللون المختار
                            paint.setColor(color);
                            updateMyViewPaint();
                            updateSelectedColorFeedback(SelectColor);
                            playColorSelectSound();

                            // إغلاق Dialog
                            if (currentColorDialog != null) {
                                currentColorDialog.dismiss();
                            }
                        }
                    });

                    rowLayout.addView(colorButton);
                }

                mainLayout.addView(rowLayout);
            }

            builder.setView(mainLayout);

            // إضافة زر إلغاء
            builder.setNegativeButton("إلغاء", new android.content.DialogInterface.OnClickListener() {
                @Override
                public void onClick(android.content.DialogInterface dialog, int which) {
                    dialog.dismiss();
                }
            });

            // إنشاء وعرض Dialog
            currentColorDialog = builder.create();
            currentColorDialog.show();

        } catch (Exception e) {
            // في حالة الخطأ، استخدم لون افتراضي
            paint.setColor(android.graphics.Color.parseColor("#FF6B9D"));
            updateMyViewPaint();
            updateSelectedColorFeedback(SelectColor);
            playColorSelectSound();
        }
    }

    /**
     * تحديث التغذية البصرية للون المحدد
     */
    private void updateSelectedColorFeedback(ImageButton selectedButton) {
        try {
            // إزالة التحديد من الزر السابق
            if (currentSelectedColorButton != null) {
                currentSelectedColorButton.setScaleX(1.0f);
                currentSelectedColorButton.setScaleY(1.0f);
                currentSelectedColorButton.setAlpha(1.0f);
            }

            // تطبيق التحديد على الزر الجديد
            if (selectedButton != null) {
                selectedButton.setScaleX(1.2f);
                selectedButton.setScaleY(1.2f);
                selectedButton.setAlpha(0.8f);
                currentSelectedColorButton = selectedButton;
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error updating color feedback: " + e.getMessage());
        }
    }

    /**
     * التراجع المتدرج عن آخر عمل
     */
    private void undoLastAction() {
        try {
            if (myView != null) {
                // التحقق من إمكانية التراجع
                if (myView.canUndo()) {
                    myView.undoMethod();

                    // تشغيل صوت التراجع
                    playUndoSound();

                    // عرض رسالة تأكيد مع انيميشن
                    showUndoConfirmation();

                    Log.d("MainActivityClean", "Undo action performed successfully");
                } else {
                    // لا توجد خطوات للتراجع
                    Toast.makeText(this, "لا توجد خطوات للتراجع عنها", Toast.LENGTH_SHORT).show();
                    Log.d("MainActivityClean", "No actions to undo");
                }
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error undoing action: " + e.getMessage());
            Toast.makeText(this, "حدث خطأ في التراجع", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * تشغيل صوت التراجع
     */
    private void playUndoSound() {
        try {
            boolean soundEnabled = preferences.getBoolean("sound_effects", true);
            if (soundEnabled && soundPool != null && colorSelectSoundId != 0) {
                // استخدام صوت اختيار اللون كصوت للتراجع
                soundPool.play(colorSelectSoundId, 1.0f, 1.0f, 1, 0, 1.0f);
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error playing undo sound: " + e.getMessage());
        }
    }

    /**
     * عرض رسالة تأكيد التراجع مع انيميشن
     */
    private void showUndoConfirmation() {
        try {
            // إنشاء Toast مخصص مع انيميشن
            android.widget.Toast undoToast = android.widget.Toast.makeText(this, "↶ تم التراجع", android.widget.Toast.LENGTH_SHORT);

            // تخصيص مظهر Toast
            android.view.View toastView = undoToast.getView();
            if (toastView != null) {
                toastView.setBackgroundResource(android.R.drawable.toast_frame);

                // إضافة انيميشن للـ Toast
                toastView.setAlpha(0f);
                toastView.animate()
                    .alpha(1f)
                    .setDuration(300)
                    .start();
            }

            undoToast.show();

        } catch (Exception e) {
            // في حالة الخطأ، استخدم Toast عادي
            android.widget.Toast.makeText(this, "تم التراجع", android.widget.Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * إضافة/إزالة من المفضلة
     */
    private void toggleFavorite() {
        try {
            boolean isFavorite = favoritesManager.isFavorite(code, position);

            if (isFavorite) {
                // إزالة من المفضلة
                if (favoritesManager.removeFromFavorites(code, position)) {
                    Toast.makeText(this, "تم إزالة الصورة من المفضلة", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(this, "خطأ في إزالة الصورة من المفضلة", Toast.LENGTH_SHORT).show();
                }
            } else {
                // إضافة للمفضلة
                if (favoritesManager.addToFavorites(code, position)) {
                    Toast.makeText(this, "تم إضافة الصورة للمفضلة", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(this, "خطأ في إضافة الصورة للمفضلة", Toast.LENGTH_SHORT).show();
                }
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error toggling favorite: " + e.getMessage());
            Toast.makeText(this, "خطأ في إدارة المفضلة", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // تنظيف الموارد
        try {
            if (mPlayer != null) {
                if (mPlayer.isPlaying()) {
                    mPlayer.stop();
                }
                mPlayer.release();
                mPlayer = null;
            }

            if (soundPool != null) {
                soundPool.release();
                soundPool = null;
            }
        } catch (Exception e) {
            Log.e("MainActivityClean", "Error in onDestroy: " + e.getMessage());
        }
    }

}
