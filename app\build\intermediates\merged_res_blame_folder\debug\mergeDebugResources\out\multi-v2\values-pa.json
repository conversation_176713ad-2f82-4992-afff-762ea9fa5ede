{"logs": [{"outputFile": "com.alwan.kids2025.app-mergeDebugResources-62:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\624bef4dd02dccf90d82100848c6b989\\transformed\\appcompat-1.7.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,14074", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,14149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3980eaeb5be19d054e57b3fb1dced5a4\\transformed\\core-1.13.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "38,39,40,41,42,43,44,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3400,3498,3600,3703,3804,3906,4004,14388", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "3493,3595,3698,3799,3901,3999,4128,14484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6d4e9d128fcd43e5a6e86ce4f6eb5926\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4436,4543,4716,4846,4955,5102,5231,5344,5598,5760,5869,6042,6174,6327,6488,6553,6619", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "4538,4711,4841,4950,5097,5226,5339,5442,5755,5864,6037,6169,6322,6483,6548,6614,6696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fe7112e8008236a43395ec4960536a6a\\transformed\\preference-1.2.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,262,341,488,657,737", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "172,257,336,483,652,732,810"}, "to": {"startLines": "66,70,138,151,164,165,166", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6701,6998,12330,13526,14489,14658,14738", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "6768,7078,12404,13668,14653,14733,14811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\041427d19b31fae88f13d56248b66aa4\\transformed\\material-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1047,1111,1203,1272,1331,1416,1479,1541,1599,1663,1724,1778,1892,1950,2010,2064,2134,2261,2342,2432,2531,2628,2707,2842,2918,2995,3124,3208,3290,3345,3400,3466,3535,3612,3683,3762,3830,3906,3976,4041,4143,4238,4311,4405,4498,4572,4641,4735,4791,4874,4941,5025,5113,5175,5239,5302,5369,5466,5572,5663,5765,5824,5883,5960,6045,6121", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "258,335,414,495,594,683,791,903,986,1042,1106,1198,1267,1326,1411,1474,1536,1594,1658,1719,1773,1887,1945,2005,2059,2129,2256,2337,2427,2526,2623,2702,2837,2913,2990,3119,3203,3285,3340,3395,3461,3530,3607,3678,3757,3825,3901,3971,4036,4138,4233,4306,4400,4493,4567,4636,4730,4786,4869,4936,5020,5108,5170,5234,5297,5364,5461,5567,5658,5760,5819,5878,5955,6040,6116,6189"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,150,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2975,3052,3131,3212,3311,4133,4241,4353,6878,6934,7083,7493,7562,7621,7706,7769,7831,7889,7953,8014,8068,8182,8240,8300,8354,8424,8551,8632,8722,8821,8918,8997,9132,9208,9285,9414,9498,9580,9635,9690,9756,9825,9902,9973,10052,10120,10196,10266,10331,10433,10528,10601,10695,10788,10862,10931,11025,11081,11164,11231,11315,11403,11465,11529,11592,11659,11756,11862,11953,12055,12114,13449,14154,14239,14315", "endLines": "5,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,150,160,161,162", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "308,3047,3126,3207,3306,3395,4236,4348,4431,6929,6993,7170,7557,7616,7701,7764,7826,7884,7948,8009,8063,8177,8235,8295,8349,8419,8546,8627,8717,8816,8913,8992,9127,9203,9280,9409,9493,9575,9630,9685,9751,9820,9897,9968,10047,10115,10191,10261,10326,10428,10523,10596,10690,10783,10857,10926,11020,11076,11159,11226,11310,11398,11460,11524,11587,11654,11751,11857,11948,12050,12109,12168,13521,14234,14310,14383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c41e8149bb8e825c8201b1dfddeb0e8d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5447", "endColumns": "150", "endOffsets": "5593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f13dd0690544d3555e27affebacc963d\\transformed\\jetified-play-services-ads-23.5.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,246,292,344,410,479,583,647,781,896,1015,1069,1128,1251,1340,1384,1467,1505,1544,1589,1672,1713", "endColumns": "46,45,51,65,68,103,63,133,114,118,53,58,122,88,43,82,37,38,44,82,40,55", "endOffsets": "245,291,343,409,478,582,646,780,895,1014,1068,1127,1250,1339,1383,1466,1504,1543,1588,1671,1712,1768"}, "to": {"startLines": "135,136,137,139,140,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157,158,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12173,12224,12274,12409,12479,12552,12660,12728,12866,12985,13108,13166,13229,13356,13673,13721,13808,13850,13893,13942,14029,14816", "endColumns": "50,49,55,69,72,107,67,137,118,122,57,62,126,92,47,86,41,42,48,86,44,59", "endOffsets": "12219,12269,12325,12474,12547,12655,12723,12861,12980,13103,13161,13224,13351,13444,13716,13803,13845,13888,13937,14024,14069,14871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0f6c2614959f9583c84a75a13e2222db\\transformed\\browser-1.8.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "67,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6773,7175,7276,7390", "endColumns": "104,100,113,102", "endOffsets": "6873,7271,7385,7488"}}]}]}