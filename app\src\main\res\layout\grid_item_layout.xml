<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:strokeColor="@color/outline_variant"
    app:strokeWidth="1dp"
    app:rippleColor="@color/primary_container">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp">

        <!-- Main Image -->
        <ImageView
            android:id="@+id/image"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_centerInParent="true"
            android:adjustViewBounds="true"
            android:contentDescription="@string/coloring_image"
            android:scaleType="centerInside"
            android:src="@drawable/gp1_1"
            android:background="@drawable/image_background" />

        <!-- Favorite Button -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/favoriteCard"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_margin="8dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackgroundBorderless"
            app:cardBackgroundColor="@android:color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:rippleColor="@color/error_container"
            app:strokeColor="@color/outline_variant"
            app:strokeWidth="1dp">

            <ImageView
                android:id="@+id/favoriteIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:contentDescription="@string/add_to_favorites"
                android:src="@drawable/ic_favorite_border"
                app:tint="@color/on_surface_variant" />

        </com.google.android.material.card.MaterialCardView>

    </RelativeLayout>

</com.google.android.material.card.MaterialCardView>