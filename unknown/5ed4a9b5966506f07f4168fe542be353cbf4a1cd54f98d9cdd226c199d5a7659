package com.alwan.kids2025;

import android.content.Context;
import android.content.SharedPreferences;
import java.util.HashSet;
import java.util.Set;

public class FavoritesManager {
    
    private static final String PREFS_NAME = "favorites_prefs";
    private static final String FAVORITES_KEY = "favorite_images";

    private Context context;
    private SharedPreferences prefs;

    public FavoritesManager(Context context) {
        this.context = context;
        prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    // إضافة صورة للمفضلة
    public void addToFavorites(String imageId) {
        Set<String> favorites = getFavorites();
        favorites.add(imageId);
        saveFavorites(favorites);
    }
    
    // إزالة صورة من المفضلة
    public void removeFromFavorites(String imageId) {
        Set<String> favorites = getFavorites();
        favorites.remove(imageId);
        saveFavorites(favorites);
    }
    
    // التحقق من وجود صورة في المفضلة
    public boolean isFavorite(String imageId) {
        return getFavorites().contains(imageId);
    }
    
    // الحصول على جميع المفضلة
    public Set<String> getFavorites() {
        return new HashSet<>(prefs.getStringSet(FAVORITES_KEY, new HashSet<>()));
    }
    
    // حفظ المفضلة
    private void saveFavorites(Set<String> favorites) {
        prefs.edit().putStringSet(FAVORITES_KEY, favorites).apply();
    }
    
    // مسح جميع المفضلة
    public void clearAllFavorites() {
        prefs.edit().remove(FAVORITES_KEY).apply();
    }
    
    // عدد المفضلة
    public int getFavoritesCount() {
        return getFavorites().size();
    }

    // إضافة صورة للمفضلة مع كائن ImageItem
    public void addFavorite(String imageKey, ImageItem imageItem) {
        addToFavorites(imageKey);
    }

    // إزالة صورة من المفضلة
    public void removeFavorite(String imageKey) {
        removeFromFavorites(imageKey);
    }

    // إضافة صورة للمفضلة باستخدام code و position (simplified version)
    public boolean addToFavorites(int code, int position) {
        try {
            String imageKey = "gp" + code + "_" + position;
            Set<String> favorites = getFavorites();
            favorites.add(imageKey);
            saveFavorites(favorites);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // إزالة صورة من المفضلة باستخدام code و position
    public boolean removeFromFavorites(int code, int position) {
        try {
            String imageKey = "gp" + code + "_" + position;
            Set<String> favorites = getFavorites();
            favorites.remove(imageKey);
            saveFavorites(favorites);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // التحقق من وجود صورة في المفضلة باستخدام code و position
    public boolean isFavorite(int code, int position) {
        String imageKey = "gp" + code + "_" + position;
        Set<String> favorites = getFavorites();
        return favorites.contains(imageKey);
    }
}
