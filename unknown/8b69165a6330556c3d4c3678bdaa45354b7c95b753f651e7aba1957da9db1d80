package com.alwan.kids2025.managers;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import androidx.annotation.NonNull;

import com.alwan.kids2025.R;
import com.google.android.play.core.review.ReviewInfo;
import com.google.android.play.core.review.ReviewManager;
import com.google.android.play.core.review.ReviewManagerFactory;

/**
 * SimpleReviewManager - إدارة بسيطة للتقييمات داخل التطبيق
 * يعرض نافذة التقييم في الأوقات المناسبة
 */
public class SimpleReviewManager {
    private static final String TAG = "SimpleReviewManager";
    private static final String PREFS_NAME = "review_prefs";
    private static final String KEY_LAST_REVIEW_REQUEST = "last_review_request";
    private static final String KEY_REVIEW_COMPLETED = "review_completed";
    private static final String KEY_APP_LAUNCHES = "app_launches";
    private static final String KEY_IMAGES_COLORED = "images_colored";
    
    // Review timing constants
    private static final long REVIEW_COOLDOWN_PERIOD = 7L * 24 * 60 * 60 * 1000; // 7 days
    private static final int MIN_APP_LAUNCHES = 3; // Minimum app launches before showing review
    private static final int MIN_IMAGES_COLORED = 2; // Minimum images colored before showing review

    private final Activity activity;
    private final ReviewManager reviewManager;
    private final SharedPreferences preferences;

    public SimpleReviewManager(@NonNull Activity activity) {
        this.activity = activity;
        this.reviewManager = ReviewManagerFactory.create(activity);
        this.preferences = activity.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    /**
     * تسجيل تشغيل التطبيق
     */
    public void recordAppLaunch() {
        int launches = preferences.getInt(KEY_APP_LAUNCHES, 0);
        preferences.edit().putInt(KEY_APP_LAUNCHES, launches + 1).apply();
        Log.d(TAG, "App launches recorded: " + (launches + 1));
    }

    /**
     * تسجيل تلوين صورة
     */
    public void recordImageColored() {
        int imagesColored = preferences.getInt(KEY_IMAGES_COLORED, 0);
        preferences.edit().putInt(KEY_IMAGES_COLORED, imagesColored + 1).apply();
        Log.d(TAG, "Images colored recorded: " + (imagesColored + 1));
        
        // Check if we should show review after coloring
        checkAndShowReview();
    }

    /**
     * فحص وعرض التقييم إذا كانت الشروط مناسبة
     */
    public void checkAndShowReview() {
        if (!shouldShowReview()) {
            return;
        }

        Log.d(TAG, "Requesting review flow");
        requestReviewFlow();
    }

    /**
     * فحص ما إذا كان يجب عرض التقييم
     */
    private boolean shouldShowReview() {
        // Check if review was already completed
        if (preferences.getBoolean(KEY_REVIEW_COMPLETED, false)) {
            Log.d(TAG, "Review already completed, skipping");
            return false;
        }

        // Check cooldown period
        long lastReviewRequest = preferences.getLong(KEY_LAST_REVIEW_REQUEST, 0);
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastReviewRequest < REVIEW_COOLDOWN_PERIOD) {
            Log.d(TAG, "Review cooldown period active, skipping");
            return false;
        }

        // Check minimum requirements
        int appLaunches = preferences.getInt(KEY_APP_LAUNCHES, 0);
        int imagesColored = preferences.getInt(KEY_IMAGES_COLORED, 0);

        if (appLaunches < MIN_APP_LAUNCHES) {
            Log.d(TAG, "Not enough app launches: " + appLaunches + "/" + MIN_APP_LAUNCHES);
            return false;
        }

        if (imagesColored < MIN_IMAGES_COLORED) {
            Log.d(TAG, "Not enough images colored: " + imagesColored + "/" + MIN_IMAGES_COLORED);
            return false;
        }

        return true;
    }

    /**
     * طلب تدفق التقييم
     */
    private void requestReviewFlow() {
        try {
            reviewManager.requestReviewFlow().addOnCompleteListener(task -> {
                if (task.isSuccessful()) {
                    ReviewInfo reviewInfo = task.getResult();
                    launchReviewFlow(reviewInfo);
                } else {
                    Log.e(TAG, "Failed to request review flow", task.getException());
                    handleReviewFailure();
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Exception in requestReviewFlow", e);
            handleReviewFailure();
        }
    }

    /**
     * تشغيل تدفق التقييم
     */
    private void launchReviewFlow(ReviewInfo reviewInfo) {
        try {
            reviewManager.launchReviewFlow(activity, reviewInfo).addOnCompleteListener(task -> {
                if (task.isSuccessful()) {
                    Log.d(TAG, "Review flow launched successfully");
                    handleReviewSuccess();
                } else {
                    Log.e(TAG, "Failed to launch review flow", task.getException());
                    handleReviewFailure();
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Exception in launchReviewFlow", e);
            handleReviewFailure();
        }
    }

    /**
     * معالجة نجاح التقييم
     */
    private void handleReviewSuccess() {
        long currentTime = System.currentTimeMillis();
        preferences.edit()
                .putLong(KEY_LAST_REVIEW_REQUEST, currentTime)
                .putBoolean(KEY_REVIEW_COMPLETED, true)
                .apply();
        
        Log.d(TAG, "Review completed successfully");
    }

    /**
     * معالجة فشل التقييم
     */
    private void handleReviewFailure() {
        long currentTime = System.currentTimeMillis();
        preferences.edit()
                .putLong(KEY_LAST_REVIEW_REQUEST, currentTime)
                .apply();
        
        Log.d(TAG, "Review failed or dismissed");
    }

    /**
     * إعادة تعيين حالة التقييم (للاختبار)
     */
    public void resetReviewState() {
        preferences.edit()
                .remove(KEY_LAST_REVIEW_REQUEST)
                .remove(KEY_REVIEW_COMPLETED)
                .apply();
        
        Log.d(TAG, "Review state reset");
    }

    /**
     * الحصول على إحصائيات التقييم
     */
    public String getReviewStats() {
        int appLaunches = preferences.getInt(KEY_APP_LAUNCHES, 0);
        int imagesColored = preferences.getInt(KEY_IMAGES_COLORED, 0);
        boolean reviewCompleted = preferences.getBoolean(KEY_REVIEW_COMPLETED, false);
        long lastRequestTime = preferences.getLong(KEY_LAST_REVIEW_REQUEST, 0);
        
        return String.format("App Launches: %d, Images Colored: %d, Review Completed: %b, Last Request: %d",
                appLaunches, imagesColored, reviewCompleted, lastRequestTime);
    }
}
