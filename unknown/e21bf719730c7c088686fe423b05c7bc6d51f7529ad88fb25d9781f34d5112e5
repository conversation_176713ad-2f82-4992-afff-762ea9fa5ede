<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android"   >
    <item >
        <shape android:shape="rectangle"  >
            <corners android:radius="5dp" />
            <solid  android:color="@color/colorPrimary" />

        </shape>
    </item>
    <item android:state_pressed="true" >
        <shape android:shape="rectangle"  >
            <corners android:radius="5dp" />
            <solid  android:color="@color/colorAccent" />

        </shape>
    </item>
    <item android:state_focused="true">
        <shape android:shape="rectangle"  >
            <corners android:radius="5dp" />
            <solid android:color="@color/colorPrimary"/>

        </shape>
    </item>

</selector>