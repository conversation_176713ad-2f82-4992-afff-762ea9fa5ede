package com.alwan.kids2025;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class FavoritesAdapter extends RecyclerView.Adapter<FavoritesAdapter.FavoriteViewHolder> {
    
    private Context context;
    private List<String> favoriteImageIds;
    private FavoritesManager favoritesManager;
    private OnFavoriteRemovedListener listener;
    
    public interface OnFavoriteRemovedListener {
        void onFavoriteRemoved();
    }
    
    public FavoritesAdapter(Context context, List<String> favoriteImageIds, OnFavoriteRemovedListener listener) {
        this.context = context;
        this.favoriteImageIds = favoriteImageIds;
        this.favoritesManager = new FavoritesManager(context);
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public FavoriteViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.favorite_item_layout, parent, false);
        return new FavoriteViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull FavoriteViewHolder holder, int position) {
        String imageId = favoriteImageIds.get(position);
        
        // تحميل الصورة
        loadImageFromId(holder.imageView, imageId);
        
        // إعداد النقر على الصورة لفتحها في MainActivityClean
        holder.imageView.setOnClickListener(v -> {
            Toast.makeText(context, "فتح الصورة: " + imageId, Toast.LENGTH_SHORT).show();
            openImageInMainActivityClean(imageId);
        });
        
        // إعداد زر إزالة المفضلة
        holder.favoriteIcon.setOnClickListener(v -> {
            favoritesManager.removeFromFavorites(imageId);
            favoriteImageIds.remove(position);
            notifyItemRemoved(position);
            notifyItemRangeChanged(position, favoriteImageIds.size());
            Toast.makeText(context, context.getString(R.string.removed_from_favorites), Toast.LENGTH_SHORT).show();
            
            if (listener != null) {
                listener.onFavoriteRemoved();
            }
        });
        
        // إعداد الضغط المطول لإزالة المفضلة
        holder.itemView.setOnLongClickListener(v -> {
            favoritesManager.removeFromFavorites(imageId);
            favoriteImageIds.remove(position);
            notifyItemRemoved(position);
            notifyItemRangeChanged(position, favoriteImageIds.size());
            Toast.makeText(context, context.getString(R.string.removed_from_favorites), Toast.LENGTH_SHORT).show();
            
            if (listener != null) {
                listener.onFavoriteRemoved();
            }
            return true;
        });
    }
    
    @Override
    public int getItemCount() {
        return favoriteImageIds.size();
    }
    
    private void loadImageFromId(ImageView imageView, String imageId) {
        try {
            // استخراج معرف الصورة من النص
            int resourceId = context.getResources().getIdentifier(imageId, "drawable", context.getPackageName());
            if (resourceId != 0) {
                Bitmap bitmap = BitmapFactory.decodeResource(context.getResources(), resourceId);
                if (bitmap != null) {
                    imageView.setImageBitmap(bitmap);
                } else {
                    // فشل في تحميل الصورة
                    Log.w("FavoritesAdapter", "Failed to decode bitmap for: " + imageId);
                    imageView.setImageResource(R.drawable.gp1_1);
                }
            } else {
                // صورة غير موجودة
                Log.w("FavoritesAdapter", "Image resource not found: " + imageId);
                imageView.setImageResource(R.drawable.gp1_1);
            }
        } catch (Exception e) {
            // صورة افتراضية في حالة حدوث خطأ
            Log.e("FavoritesAdapter", "Error loading image: " + imageId + " - " + e.getMessage());
            imageView.setImageResource(R.drawable.gp1_1);
        }
    }
    
    private void openImageInMainActivityClean(String imageId) {
        try {
            Log.d("FavoritesAdapter", "Attempting to open image in MainActivityClean: " + imageId);

            // استخراج code و position من imageId
            // imageId format: "gp{code}_{position}"
            String[] parts = imageId.replace("gp", "").split("_");
            if (parts.length == 2) {
                int code = Integer.parseInt(parts[0]);
                int position = Integer.parseInt(parts[1]);

                Log.d("FavoritesAdapter", "Parsed code: " + code + ", position: " + position);

                // التحقق من وجود الصورة قبل فتحها
                int resourceId = context.getResources().getIdentifier(imageId, "drawable", context.getPackageName());
                if (resourceId == 0) {
                    Log.w("FavoritesAdapter", "Image resource not found: " + imageId);
                    Toast.makeText(context, "الصورة غير متوفرة", Toast.LENGTH_SHORT).show();
                    return;
                }

                Log.d("FavoritesAdapter", "Image resource found, starting MainActivityClean");

                Intent intent = new Intent(context, MainActivityClean.class);
                intent.putExtra("code", code);
                intent.putExtra("position", position);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);

                // إضافة تأخير قصير لضمان معالجة النقر
                new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                    try {
                        context.startActivity(intent);
                        Log.d("FavoritesAdapter", "MainActivityClean started successfully");
                    } catch (Exception e) {
                        Log.e("FavoritesAdapter", "Error starting MainActivityClean: " + e.getMessage());
                        Toast.makeText(context, "خطأ في فتح الصورة", Toast.LENGTH_SHORT).show();
                    }
                }, 100);

            } else {
                Log.e("FavoritesAdapter", "Invalid imageId format: " + imageId);
                Toast.makeText(context, "معرف صورة غير صحيح", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Log.e("FavoritesAdapter", "Error opening image: " + imageId + " - " + e.getMessage());
            Toast.makeText(context, "خطأ في فتح الصورة", Toast.LENGTH_SHORT).show();
        }
    }
    
    static class FavoriteViewHolder extends RecyclerView.ViewHolder {
        ImageView imageView;
        ImageView favoriteIcon;
        CardView favoriteCard;
        
        public FavoriteViewHolder(@NonNull View itemView) {
            super(itemView);
            imageView = itemView.findViewById(R.id.favorite_image);
            favoriteIcon = itemView.findViewById(R.id.favorite_remove_icon);
            favoriteCard = itemView.findViewById(R.id.favorite_remove_card);
        }
    }
}
