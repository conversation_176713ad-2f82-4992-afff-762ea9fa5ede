package com.alwan.kids2025;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.view.WindowManager;

import java.util.ArrayList;
import java.util.Stack;

public class MyView extends View {

    private static final String TAG = "MyView";
    
    public Bitmap mBitmap, scaledBitmap;
    private Canvas mCanvas;
    private Path mPath;
    private Paint mPaint;
    private ArrayList<Path> paths = new ArrayList<>();
    private ArrayList<Paint> paints = new ArrayList<>();
    private ArrayList<Path> undonePaths = new ArrayList<>();
    private ArrayList<Paint> undonePaints = new ArrayList<>();
    
    private float mX, mY;
    private static final float TOUCH_TOLERANCE = 4;
    
    private Context context;
    private WindowManager windowManager;
    private int screenWidth, screenHeight;



    // أداة الرسم الحالية
    private DrawingTool currentTool = DrawingTool.BRUSH;

    public MyView(Context context, WindowManager windowManager) {
        super(context);
        this.context = context;
        this.windowManager = windowManager;
        
        // الحصول على أبعاد الشاشة
        getScreenDimensions();
        
        // تهيئة الرسم
        initializePaint();

        // تحميل الصورة
        loadImage();

        Log.d(TAG, "MyView initialized with screen dimensions: " + screenWidth + "x" + screenHeight);
    }
    
    private void getScreenDimensions() {
        DisplayMetrics displayMetrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getMetrics(displayMetrics);
        screenWidth = displayMetrics.widthPixels;
        screenHeight = displayMetrics.heightPixels;
    }
    
    private void initializePaint() {
        mPath = new Path();
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setColor(Color.BLACK);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeJoin(Paint.Join.ROUND);
        mPaint.setStrokeCap(Paint.Cap.ROUND);
        mPaint.setStrokeWidth(15);
    }


    
    private void loadImage() {
        try {
            MainActivity mainActivity = (MainActivity) context;
            int position = mainActivity.position;
            int code = mainActivity.code;

            // بناء اسم الصورة بالنمط الصحيح: gp{code}_{position}
            String imageName = "gp" + code + "_" + position;
            Log.d(TAG, "Loading image: " + imageName);

            // تحميل الصورة من drawable resources
            int resourceId = context.getResources().getIdentifier(imageName, "drawable", context.getPackageName());

            if (resourceId != 0) {
                mBitmap = BitmapFactory.decodeResource(context.getResources(), resourceId);
                if (mBitmap != null) {
                    // تحجيم الصورة لتناسب الشاشة مع الحفاظ على النسبة
                    scaledBitmap = createScaledBitmap(mBitmap, screenWidth, screenHeight);

                    // إنشاء canvas للرسم
                    mCanvas = new Canvas(scaledBitmap);

                    Log.d(TAG, "Image loaded successfully: " + imageName + " (Original: " + mBitmap.getWidth() + "x" + mBitmap.getHeight() + ", Scaled: " + scaledBitmap.getWidth() + "x" + scaledBitmap.getHeight() + ")");
                } else {
                    Log.e(TAG, "Failed to decode bitmap for: " + imageName);
                    createDefaultBitmap();
                }
            } else {
                Log.e(TAG, "Resource not found for: " + imageName + ". Trying alternative naming patterns...");
                // محاولة أنماط أسماء بديلة
                tryAlternativeImageNames(code, position);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading image: " + e.getMessage());
            e.printStackTrace();
            createDefaultBitmap();
        }
    }

    /**
     * محاولة تحميل الصورة بأنماط أسماء مختلفة
     */
    private void tryAlternativeImageNames(int code, int position) {
        String[] patterns = {
            "c" + code + "_" + position,  // النمط القديم
            "image_" + code + "_" + position,  // نمط بديل
            "coloring_" + code + "_" + position,  // نمط آخر
            "gp" + code + "_" + String.format("%02d", position)  // مع padding
        };

        for (String pattern : patterns) {
            Log.d(TAG, "Trying alternative pattern: " + pattern);
            int resourceId = context.getResources().getIdentifier(pattern, "drawable", context.getPackageName());

            if (resourceId != 0) {
                mBitmap = BitmapFactory.decodeResource(context.getResources(), resourceId);
                if (mBitmap != null) {
                    scaledBitmap = createScaledBitmap(mBitmap, screenWidth, screenHeight);
                    mCanvas = new Canvas(scaledBitmap);
                    Log.d(TAG, "Alternative image loaded successfully: " + pattern);
                    return;
                }
            }
        }

        Log.w(TAG, "No image found for code: " + code + ", position: " + position + ". Creating default bitmap.");
        createDefaultBitmap();
    }

    /**
     * إنشاء bitmap مُحجم مع الحفاظ على النسبة والتوسيط
     */
    private Bitmap createScaledBitmap(Bitmap originalBitmap, int targetWidth, int targetHeight) {
        if (originalBitmap == null) {
            return null;
        }

        int originalWidth = originalBitmap.getWidth();
        int originalHeight = originalBitmap.getHeight();

        // حساب النسبة للحفاظ على aspect ratio
        float scaleX = (float) targetWidth / originalWidth;
        float scaleY = (float) targetHeight / originalHeight;
        float scale = Math.min(scaleX, scaleY); // استخدام أصغر نسبة للحفاظ على النسبة

        int scaledWidth = Math.round(originalWidth * scale);
        int scaledHeight = Math.round(originalHeight * scale);

        // إنشاء bitmap مُحجم مع تحسين الجودة
        Bitmap scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, scaledWidth, scaledHeight, true);

        // تحسين التباين والوضوح
        scaledBitmap = enhanceImageClarity(scaledBitmap);

        // إنشاء bitmap نهائي بحجم الشاشة مع خلفية بيضاء
        Bitmap finalBitmap = Bitmap.createBitmap(targetWidth, targetHeight, Bitmap.Config.ARGB_8888);
        finalBitmap.eraseColor(Color.WHITE);

        // رسم الصورة المُحجمة في المنتصف مع تحسينات إضافية
        Canvas canvas = new Canvas(finalBitmap);
        Paint drawPaint = new Paint();
        drawPaint.setAntiAlias(true);
        drawPaint.setFilterBitmap(true);
        drawPaint.setDither(true);

        int left = (targetWidth - scaledWidth) / 2;
        int top = (targetHeight - scaledHeight) / 2;
        canvas.drawBitmap(scaledBitmap, left, top, drawPaint);

        // تنظيف الذاكرة
        if (scaledBitmap != originalBitmap) {
            scaledBitmap.recycle();
        }

        return finalBitmap;
    }

    /**
     * تحسين وضوح وتباين الصورة
     */
    private Bitmap enhanceImageClarity(Bitmap originalBitmap) {
        if (originalBitmap == null) return null;

        int width = originalBitmap.getWidth();
        int height = originalBitmap.getHeight();

        // إنشاء bitmap جديد محسن
        Bitmap enhancedBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(enhancedBitmap);

        // إنشاء Paint مع تحسينات
        Paint enhancePaint = new Paint();
        enhancePaint.setAntiAlias(true);
        enhancePaint.setFilterBitmap(true);
        enhancePaint.setDither(true);

        // تحسين التباين والحدة
        ColorMatrix colorMatrix = new ColorMatrix();
        // زيادة التباين قليلاً
        colorMatrix.set(new float[] {
            1.2f, 0, 0, 0, 0,      // Red
            0, 1.2f, 0, 0, 0,      // Green
            0, 0, 1.2f, 0, 0,      // Blue
            0, 0, 0, 1, 0          // Alpha
        });

        ColorMatrixColorFilter colorFilter = new ColorMatrixColorFilter(colorMatrix);
        enhancePaint.setColorFilter(colorFilter);

        // رسم الصورة المحسنة
        canvas.drawBitmap(originalBitmap, 0, 0, enhancePaint);

        return enhancedBitmap;
    }

    private void createDefaultBitmap() {
        // إنشاء bitmap افتراضي أبيض مع رسالة تشخيصية
        scaledBitmap = Bitmap.createBitmap(screenWidth, screenHeight, Bitmap.Config.ARGB_8888);
        scaledBitmap.eraseColor(Color.WHITE);
        mCanvas = new Canvas(scaledBitmap);

        // رسم نص تشخيصي على الشاشة البيضاء
        Paint textPaint = new Paint();
        textPaint.setColor(Color.RED);
        textPaint.setTextSize(48);
        textPaint.setAntiAlias(true);

        MainActivity mainActivity = (MainActivity) context;
        String debugText = "Image not found: gp" + mainActivity.code + "_" + mainActivity.position;
        mCanvas.drawText(debugText, 50, screenHeight / 2, textPaint);

        Log.e(TAG, "Created default white bitmap - " + debugText);
    }
    
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        
        if (scaledBitmap == null) {
            scaledBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888);
            mCanvas = new Canvas(scaledBitmap);
        }
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if (scaledBitmap != null) {
            canvas.drawBitmap(scaledBitmap, 0, 0, null);
        }

        // رسم المسارات الحالية
        for (int i = 0; i < paths.size(); i++) {
            canvas.drawPath(paths.get(i), paints.get(i));
        }

        // رسم المسار الحالي
        canvas.drawPath(mPath, mPaint);
    }
    
    private void touch_start(float x, float y) {
        mPath.reset();
        mPath.moveTo(x, y);
        mX = x;
        mY = y;
        
        // إضافة النقطة إلى قائمة النقاط المرسومة
        MainActivity.drawnPoints.add(new Point((int) x, (int) y));
    }
    
    private void touch_move(float x, float y) {
        float dx = Math.abs(x - mX);
        float dy = Math.abs(y - mY);
        if (dx >= TOUCH_TOLERANCE || dy >= TOUCH_TOLERANCE) {
            mPath.quadTo(mX, mY, (x + mX) / 2, (y + mY) / 2);
            mX = x;
            mY = y;
            
            // إضافة النقطة إلى قائمة النقاط المرسومة
            MainActivity.drawnPoints.add(new Point((int) x, (int) y));
        }
    }
    
    private void touch_up() {
        mPath.lineTo(mX, mY);
        
        // حفظ المسار والطلاء الحاليين
        paths.add(new Path(mPath));
        Paint newPaint = new Paint(mPaint);
        paints.add(newPaint);
        
        // رسم المسار على الـ bitmap
        if (mCanvas != null) {
            mCanvas.drawPath(mPath, mPaint);
        }
        
        // إعادة تعيين المسار
        mPath.reset();
        
        // مسح قائمة الـ undo
        undonePaths.clear();
        undonePaints.clear();
    }
    
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        float x = event.getX();
        float y = event.getY();

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // التحقق من نوع الأداة المحددة
                if (currentTool == DrawingTool.FILL) {
                    // تنفيذ flood fill
                    int fillColor = mPaint.getColor();
                    floodFill((int) x, (int) y, fillColor);
                } else {
                    // الرسم العادي
                    touch_start(x, y);
                    invalidate();
                }
                break;
            case MotionEvent.ACTION_MOVE:
                // الرسم العادي فقط (ليس للـ fill)
                if (currentTool != DrawingTool.FILL) {
                    touch_move(x, y);
                    invalidate();
                }
                break;
            case MotionEvent.ACTION_UP:
                // الرسم العادي فقط (ليس للـ fill)
                if (currentTool != DrawingTool.FILL) {
                    touch_up();
                    invalidate();
                }
                break;
        }
        return true;
    }
    
    public void undoMethod() {
        if (paths.size() > 0) {
            // نقل آخر مسار إلى قائمة الـ undo
            undonePaths.add(paths.remove(paths.size() - 1));
            undonePaints.add(paints.remove(paints.size() - 1));
            
            // إعادة رسم الـ bitmap
            redrawBitmap();
            invalidate();
            
            Log.d(TAG, "Undo performed. Remaining paths: " + paths.size());
        }
    }
    
    public void redoMethod() {
        if (undonePaths.size() > 0) {
            // إعادة آخر مسار من قائمة الـ undo
            paths.add(undonePaths.remove(undonePaths.size() - 1));
            paints.add(undonePaints.remove(undonePaints.size() - 1));
            
            // إعادة رسم الـ bitmap
            redrawBitmap();
            invalidate();
            
            Log.d(TAG, "Redo performed. Total paths: " + paths.size());
        }
    }
    
    private void redrawBitmap() {
        if (mBitmap != null && mCanvas != null) {
            // مسح الـ canvas
            mCanvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);
            
            // رسم الصورة الأصلية
            mCanvas.drawBitmap(mBitmap, 0, 0, null);
            
            // رسم جميع المسارات
            for (int i = 0; i < paths.size(); i++) {
                mCanvas.drawPath(paths.get(i), paints.get(i));
            }
        }
    }
    
    public void clearCanvas() {
        paths.clear();
        paints.clear();
        undonePaths.clear();
        undonePaints.clear();
        MainActivity.drawnPoints.clear();
        
        redrawBitmap();
        invalidate();
        
        Log.d(TAG, "Canvas cleared");
    }
    
    public void updatePaint(Paint newPaint) {
        mPaint = new Paint(newPaint);
    }

    public Bitmap getBitmap() {
        return scaledBitmap;
    }

    /**
     * تنفيذ Flood Fill - ملء المناطق المغلقة بالألوان
     */
    public void floodFill(int x, int y, int newColor) {
        if (scaledBitmap == null || mCanvas == null) {
            Log.e(TAG, "Cannot perform flood fill - bitmap or canvas is null");
            return;
        }

        // التأكد من أن النقطة داخل حدود الصورة
        if (x < 0 || x >= scaledBitmap.getWidth() || y < 0 || y >= scaledBitmap.getHeight()) {
            Log.w(TAG, "Flood fill point outside bitmap bounds");
            return;
        }

        int targetColor = scaledBitmap.getPixel(x, y);

        // إذا كان اللون الجديد نفس اللون الموجود، لا نحتاج لفعل شيء
        if (targetColor == newColor) {
            return;
        }

        Log.d(TAG, "Starting flood fill at (" + x + ", " + y + ") with color: " + newColor);

        // تنفيذ flood fill باستخدام stack-based algorithm
        floodFillUtil(x, y, targetColor, newColor);

        // إعادة رسم الـ view
        invalidate();
    }

    /**
     * خوارزمية Flood Fill المحسنة باستخدام Stack
     */
    private void floodFillUtil(int x, int y, int targetColor, int newColor) {
        int width = scaledBitmap.getWidth();
        int height = scaledBitmap.getHeight();

        // استخدام Stack بدلاً من recursion لتجنب stack overflow
        java.util.Stack<Point> stack = new java.util.Stack<>();
        stack.push(new Point(x, y));

        while (!stack.isEmpty()) {
            Point point = stack.pop();
            int px = point.x;
            int py = point.y;

            // التحقق من الحدود
            if (px < 0 || px >= width || py < 0 || py >= height) {
                continue;
            }

            // التحقق من اللون
            if (scaledBitmap.getPixel(px, py) != targetColor) {
                continue;
            }

            // تغيير لون البكسل
            scaledBitmap.setPixel(px, py, newColor);

            // إضافة البكسلات المجاورة إلى الـ stack
            stack.push(new Point(px + 1, py));     // يمين
            stack.push(new Point(px - 1, py));     // يسار
            stack.push(new Point(px, py + 1));     // أسفل
            stack.push(new Point(px, py - 1));     // أعلى
        }
    }

    /**
     * تحديد نوع أداة الرسم
     */
    public enum DrawingTool {
        BRUSH, PENCIL, ERASER, FILL
    }

    public void setDrawingTool(DrawingTool tool) {
        this.currentTool = tool;
        Log.d(TAG, "Drawing tool changed to: " + tool);
    }

    public DrawingTool getCurrentTool() {
        return currentTool;
    }


}
