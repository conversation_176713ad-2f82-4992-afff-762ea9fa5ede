<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modern_background">

    <!-- Main Content Container -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp" />

    <!-- Bottom Navigation Bar -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_gravity="bottom"
        app:cardElevation="16dp"
        app:cardCornerRadius="0dp"
        app:cardBackgroundColor="@android:color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:background="@drawable/bottom_nav_background"
            android:paddingTop="8dp"
            android:paddingBottom="8dp">

            <!-- Home Tab -->
            <LinearLayout
                android:id="@+id/nav_home"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/bottom_nav_item_background"
                android:clickable="true"
                android:focusable="true"
                android:padding="4dp">

                <androidx.cardview.widget.CardView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginBottom="4dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/modern_primary">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_home_modern"
                        android:tint="@android:color/white" />

                </androidx.cardview.widget.CardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="الرئيسية"
                    android:textColor="@color/modern_primary"
                    android:textSize="10sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/blabeloo" />

            </LinearLayout>





            <!-- Favorites Tab -->
            <LinearLayout
                android:id="@+id/nav_favorites"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/bottom_nav_item_background"
                android:clickable="true"
                android:focusable="true"
                android:padding="4dp">

                <androidx.cardview.widget.CardView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginBottom="4dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/modern_accent">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_favorites_modern"
                        android:tint="@android:color/white" />

                </androidx.cardview.widget.CardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="المفضلة"
                    android:textColor="@color/text_primary"
                    android:textSize="10sp"
                    android:fontFamily="@font/blabeloo" />

            </LinearLayout>

            <!-- More Tab -->
            <LinearLayout
                android:id="@+id/nav_more"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/bottom_nav_item_background"
                android:clickable="true"
                android:focusable="true"
                android:padding="4dp">

                <androidx.cardview.widget.CardView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginBottom="4dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/text_secondary">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_more_modern"
                        android:tint="@android:color/white" />

                </androidx.cardview.widget.CardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="المزيد"
                    android:textColor="@color/text_primary"
                    android:textSize="10sp"
                    android:fontFamily="@font/blabeloo" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
