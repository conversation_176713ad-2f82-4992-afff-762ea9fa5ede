package com.alwan.kids2025;

import static com.google.android.gms.ads.RequestConfiguration.MAX_AD_CONTENT_RATING_G;
import static com.google.android.gms.ads.RequestConfiguration.TAG_FOR_CHILD_DIRECTED_TREATMENT_TRUE;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.media.MediaPlayer;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.StrictMode;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;

import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.view.WindowManager;
import android.view.WindowMetrics;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.preference.PreferenceManager;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.RequestConfiguration;
import com.google.android.gms.ads.appopen.AppOpenAd;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;
import com.google.android.material.snackbar.Snackbar;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.onesignal.OneSignal;
import com.onesignal.OSDeviceState;

// Google Play Enhanced Features (will be added later)
// import com.alwan.kids2025.managers.UpdateManager;
// import com.alwan.kids2025.managers.ReviewManager;
// import com.alwan.kids2025.managers.AssetManager;
import com.flask.colorpicker.ColorPickerView;
import com.flask.colorpicker.OnColorSelectedListener;
import com.flask.colorpicker.builder.ColorPickerClickListener;
import com.flask.colorpicker.builder.ColorPickerDialogBuilder;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.Queue;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainActivity extends AppCompatActivity implements View.OnClickListener, View.OnTouchListener {

    private static final int PERMISSION_REQUEST_CODE = 1;
    public static ArrayList<Point> drawnPoints = new ArrayList<>();
    public static boolean readyForReview = false;
    static String randomNumber;
    Paint paint;
    ImageView iv;
    int position, code;
    ImageButton White, Black, Gray, lightOrange, Brown, Yellow, deepBlue, lightBlue, deepPurple, lightPurple,
            deepGreen, lightGreen, deepPink, lightPink, Red, deepOrange, select_color;


 // الأداة الافتراضية
    int h, w;
    AdView mAdView;
    SharedPreferences preferences;
    MediaPlayer mPlayer, clickPlayer;
    int counter = 0;
    Point aaa;
    private RelativeLayout drawingLayout;
    private MyViewSimple myView;
    private ArrayList<Path> paths = new ArrayList<>();
    private ArrayList<Path> undonePaths = new ArrayList<>();
    private FirebaseAnalytics mFirebaseAnalytics;
    private FavoritesManager favoritesManager;
    private AppOpenAd appOpenAd;
    private Vibrator vibrator;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // تطبيق الوضع الليلي قبل تحديد المحتوى
        applyDarkModeIfEnabled();

        setContentView(R.layout.activity_main);

        // Initialize Firebase Analytics
        mFirebaseAnalytics = FirebaseAnalytics.getInstance(this);

        // Log an event to test Firebase Analytics
        if (mFirebaseAnalytics != null) {
            Bundle bundle = new Bundle();
            bundle.putString(FirebaseAnalytics.Param.METHOD, "onCreate");
            mFirebaseAnalytics.logEvent(FirebaseAnalytics.Event.APP_OPEN, bundle);
        } else {
            Log.e("MainActivity", "FirebaseAnalytics initialization failed.");
        }

        // استخراج البيانات الممررة وتسجيل OnBackPressed callback باستخدام OnBackPressedDispatcher
        Bundle extras = getIntent().getExtras();
        if (extras != null) {
            position = extras.getInt("position", 1); // قيمة افتراضية
            code = extras.getInt("code", 1); // قيمة افتراضية
            Log.d("MainActivity", "Received data - code: " + code + ", position: " + position);
        } else {
            // قيم افتراضية في حالة عدم وجود بيانات
            position = 1;
            code = 1;
            Log.w("MainActivity", "No extras found, using default values - code: " + code + ", position: " + position);
        }
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                Intent intent = new Intent(MainActivity.this, CategoryItems.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.putExtra("code", code);
                startActivity(intent);
                finish();
            }
        });

        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        randomNumber = String.valueOf(new Random().nextInt(5) + 1);

        StrictMode.VmPolicy.Builder builder = new StrictMode.VmPolicy.Builder();
        StrictMode.setVmPolicy(builder.build());

        preferences = PreferenceManager.getDefaultSharedPreferences(MainActivity.this);
        favoritesManager = new FavoritesManager(this);

        // تحضير خدمة الاهتزاز
        vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);

        MobileAds.initialize(this, new OnInitializationCompleteListener() {
            @Override
            public void onInitializationComplete(InitializationStatus initializationStatus) { }
        });
        RequestConfiguration requestConfiguration = MobileAds.getRequestConfiguration()
                .toBuilder()
                .setTagForChildDirectedTreatment(TAG_FOR_CHILD_DIRECTED_TREATMENT_TRUE)
                .setMaxAdContentRating(MAX_AD_CONTENT_RATING_G)
                .build();
        MobileAds.setRequestConfiguration(requestConfiguration);

        mAdView = findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);

        iv = findViewById(R.id.coringImage);

        drawingLayout = findViewById(R.id.relative_layout);

        // التحقق من وجود الصورة قبل إنشاء MyView
        String expectedImageName = "gp" + code + "_" + position;
        int resourceId = getResources().getIdentifier(expectedImageName, "drawable", getPackageName());

        if (resourceId == 0) {
            Log.e("MainActivity", "Image resource not found: " + expectedImageName);
            // محاولة البحث عن أنماط بديلة
            String[] alternativePatterns = {
                "c" + code + "_" + position,
                "image_" + code + "_" + position,
                "coloring_" + code + "_" + position
            };

            for (String pattern : alternativePatterns) {
                resourceId = getResources().getIdentifier(pattern, "drawable", getPackageName());
                if (resourceId != 0) {
                    Log.d("MainActivity", "Found alternative image: " + pattern);
                    break;
                }
            }
        } else {
            Log.d("MainActivity", "Image resource found: " + expectedImageName + " (ID: " + resourceId + ")");
        }

        // إنشاء MyView بعد التأكد من وجود البيانات
        myView = new MyViewSimple(this);
        drawingLayout.addView(myView);

        // MyViewSimple تم إنشاؤه بنجاح

        // تهيئة الأزرار
        initializeButtons();

        // تهيئة Paint
        initializePaint();



        // ImageButton لا يحتاج إلى Typeface لأنه يستخدم الصور وليس النص

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                Log.w("Permissions", "POST_NOTIFICATIONS permission not granted. Requesting permission...");
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.POST_NOTIFICATIONS}, 100);
            } else {
                Log.d("Permissions", "POST_NOTIFICATIONS permission already granted.");
            }
        } else {
            Log.d("MainActivity", "POST_NOTIFICATIONS permission not required for this Android version.");
        }

        // تهيئة OneSignal (v4.x API)
        MyFirebaseMessagingService.initialize(this);
        // Log OneSignal initialization start
        Log.d("OneSignal", "Initializing OneSignal...");

        // Initialize OneSignal with v4.x API
        OneSignal.setLogLevel(OneSignal.LOG_LEVEL.VERBOSE, OneSignal.LOG_LEVEL.NONE);
        OneSignal.initWithContext(this);
        OneSignal.setAppId("YOUR_ONESIGNAL_APP_ID");

        // Add a notification received handler with detailed logging (v4.x API)
        OneSignal.setNotificationWillShowInForegroundHandler(notificationReceivedEvent -> {
            Log.d("OneSignal", "Notification received: " + notificationReceivedEvent.getNotification().getBody());
            notificationReceivedEvent.complete(notificationReceivedEvent.getNotification());
        });

        // Add a notification opened handler with detailed logging (v4.x API)
        OneSignal.setNotificationOpenedHandler(result -> {
            Log.d("OneSignal", "Notification opened: " + result.getNotification().getBody());
        });

        // Log OneSignal device state with additional checks (v4.x API)
        OSDeviceState deviceState = OneSignal.getDeviceState();
        if (deviceState != null) {
            Log.d("OneSignal", "Device ID: " + deviceState.getUserId());
            Log.d("OneSignal", "Subscribed: " + deviceState.isSubscribed());
        } else {
            Log.w("OneSignal", "Device state is null");
        }

        // Ensure notification permission is requested correctly
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                Log.d("MainActivity", "Requesting POST_NOTIFICATIONS permission...");
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.POST_NOTIFICATIONS}, 100);
            } else {
                Log.d("MainActivity", "POST_NOTIFICATIONS permission already granted.");
            }
        } else {
            Log.d("MainActivity", "POST_NOTIFICATIONS permission not required for this Android version.");
        }
    }

    /**
     * تهيئة Paint للرسم
     */
    private void initializePaint() {
        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setDither(true);
        paint.setColor(Color.BLACK);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeJoin(Paint.Join.ROUND);
        paint.setStrokeCap(Paint.Cap.ROUND);
        paint.setStrokeWidth(15);

        // تمرير Paint إلى MyView
        if (myView != null) {
            myView.updatePaint(paint);
        }
    }

    /**
     * تحديث Paint في MyView بعد تغيير اللون
     */
    private void updateMyViewPaint() {
        if (myView != null && paint != null) {
            myView.updatePaint(paint);
        }
    }



    /**
     * تهيئة جميع الأزرار
     */
    private void initializeButtons() {
        // أزرار الألوان الأساسية
        White = findViewById(R.id.white);
        Black = findViewById(R.id.black);
        Gray = findViewById(R.id.gray);
        lightOrange = findViewById(R.id.light_orange);
        Brown = findViewById(R.id.brown);
        Yellow = findViewById(R.id.yellow);
        deepBlue = findViewById(R.id.deep_blue);
        lightBlue = findViewById(R.id.light_blue);
        deepPurple = findViewById(R.id.deep_purple);
        lightPurple = findViewById(R.id.light_purple);
        deepGreen = findViewById(R.id.deep_green);
        lightGreen = findViewById(R.id.light_green);
        deepPink = findViewById(R.id.deep_pink);
        lightPink = findViewById(R.id.light_pink);
        Red = findViewById(R.id.red);
        deepOrange = findViewById(R.id.deep_orange);
        select_color = findViewById(R.id.select_color);



        // تعيين المستمعين
        White.setOnClickListener(this);
        Black.setOnClickListener(this);
        Gray.setOnClickListener(this);
        lightOrange.setOnClickListener(this);
        Brown.setOnClickListener(this);
        Yellow.setOnClickListener(this);
        deepBlue.setOnClickListener(this);
        lightBlue.setOnClickListener(this);
        deepPurple.setOnClickListener(this);
        lightPurple.setOnClickListener(this);
        deepGreen.setOnClickListener(this);
        lightGreen.setOnClickListener(this);
        deepPink.setOnClickListener(this);
        lightPink.setOnClickListener(this);
        Red.setOnClickListener(this);
        deepOrange.setOnClickListener(this);
        select_color.setOnClickListener(this);


    }

    @Override
    protected void onStart() {
        super.onStart();
        loadAppOpenAd();
    }

    private void loadAppOpenAd() {
        AdRequest adRequest = new AdRequest.Builder().build();
        AppOpenAd.load(
            this,
            getString(R.string.app_open_ad_unit_id),
            adRequest,
            new AppOpenAd.AppOpenAdLoadCallback() {
                @Override
                public void onAdLoaded(AppOpenAd ad) {
                    appOpenAd = ad;
                    showAppOpenAd();
                }

                @Override
                public void onAdFailedToLoad(LoadAdError loadAdError) {
                    Log.e("AppOpenAd", "Failed to load: " + loadAdError.getMessage());
                }
            });
    }

    private void showAppOpenAd() {
        if (appOpenAd != null) {
            appOpenAd.show(this);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 100) { // Notification permission request code
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, getString(R.string.notification_permission_granted), Toast.LENGTH_SHORT).show();
                Log.d("MainActivity", "Notification permission granted.");
            } else {
                Toast.makeText(this, getString(R.string.notification_permission_denied), Toast.LENGTH_SHORT).show();
                Log.d("MainActivity", "Notification permission denied.");
            }
        }
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        return false;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.share_save_menu, menu);
        // تحديث أيقونة المفضلة عند إنشاء القائمة
        updateFavoriteMenuItem(menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_share:
                onShareImageItem();
                break;
            case R.id.action_save:
                if (ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                    requestPermission();
                } else {
                    saveImageToGallery();
                }
                break;
            case R.id.action_mute:
                if (mPlayer != null) {
                    if (!mPlayer.isPlaying()) {
                        mPlayer.start();
                        mPlayer.setLooping(true);
                        item.setIcon(R.drawable.sound_on);
                        Snackbar.make(drawingLayout, getString(R.string.soundon), Snackbar.LENGTH_LONG).show();
                    } else {
                        mPlayer.pause();
                        item.setIcon(R.drawable.sound_off);
                        Snackbar.make(drawingLayout, getString(R.string.soundoff), Snackbar.LENGTH_LONG).show();
                    }
                }
                break;
            case R.id.action_undo:
                myView.undoMethod();
                break;
            case R.id.action_favorite:
                toggleFavorite(item);
                break;
            case android.R.id.home:
                Intent i = new Intent(MainActivity.this, CategoryItems.class);
                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                i.putExtra("code", code);
                startActivity(i);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onSupportNavigateUp() {
        // معالجة إضافية لسهم الرجوع
        Intent i = new Intent(MainActivity.this, CategoryItems.class);
        i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        i.putExtra("code", code);
        startActivity(i);
        finish();
        return true;
    }

    public void onShareImageItem() {
        String package_name = getPackageName();
        Uri bmpUri = getLocalBitmapUri(iv);
        if (bmpUri != null) {
            Intent shareIntent = new Intent();
            shareIntent.setAction(Intent.ACTION_SEND);
            shareIntent.putExtra(Intent.EXTRA_STREAM, bmpUri);
            shareIntent.putExtra(Intent.EXTRA_TEXT, "شفتوا إبداعي؟!\nأنا رسمت هذه الرسمة باستخدام تطبيق ألوان الأطفال! 🎨💛\nفيه أدوات ممتعة وألوان جميلة تخليك ترسم وتلون بطريقتك الخاصة!\nحمّل التطبيق وورينا إبداعك 👇\nhttps://play.google.com/store/apps/details?id=" + package_name);
            shareIntent.setType("image/*");
            startActivity(Intent.createChooser(shareIntent, getString(R.string.action_share)));
        }
    }

    // Removed the catch block for IOException in the getLocalBitmapUri method
    public Uri getLocalBitmapUri(ImageView iv) {
        iv.setImageBitmap(myView.scaledBitmap);
        Drawable drawable = iv.getDrawable();
        Bitmap bmp = null;
        if (drawable instanceof BitmapDrawable) {
            bmp = ((BitmapDrawable) iv.getDrawable()).getBitmap();
        } else {
            return null;
        }
        try {
            File file = new File(getExternalFilesDir(Environment.DIRECTORY_PICTURES), "share_image_" + System.currentTimeMillis() + ".png");
            FileOutputStream out = new FileOutputStream(file);
            bmp.compress(Bitmap.CompressFormat.PNG, 90, out);
            out.close();
            return Uri.fromFile(file);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private void saveImageToGallery() {
        Uri bmpUri = null;
        File file;
        try {
            File dir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).getPath()
                    + "/" + getString(R.string.app_name) + "/");
            dir.mkdirs();
            String fileName = "/" + System.currentTimeMillis() + "share_image.png";
            file = new File(dir, fileName);
            FileOutputStream fos = new FileOutputStream(file);
            myView.scaledBitmap.compress(Bitmap.CompressFormat.PNG, 90, fos);
            fos.flush();
            fos.close();

            Toast.makeText(getApplicationContext(), getString(R.string.save), Toast.LENGTH_LONG).show();
            MediaScannerConnection.scanFile(MainActivity.this,
                    new String[]{file.toString()}, null,
                    new MediaScannerConnection.OnScanCompletedListener() {
                        public void onScanCompleted(String path, Uri uri) {
                            Log.i("ExternalStorage", "Scanned " + path + ":");
                            Log.i("ExternalStorage", "-> uri=" + uri);
                        }
                    });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onClick(View view) {
        enable();
        // Convert switch to enhanced switch
        switch (view.getId()) {
            case R.id.white -> {
                paint.setColor(ContextCompat.getColor(this, R.color.white));
                White.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.black -> {
                paint.setColor(ContextCompat.getColor(this, R.color.black));
                Black.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.gray -> {
                paint.setColor(ContextCompat.getColor(this, R.color.gray));
                Gray.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.light_orange -> {
                paint.setColor(ContextCompat.getColor(this, R.color.light_orange));
                lightOrange.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.brown -> {
                paint.setColor(ContextCompat.getColor(this, R.color.brown));
                Brown.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.yellow -> {
                paint.setColor(ContextCompat.getColor(this, R.color.yellow));
                Yellow.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.deep_blue -> {
                paint.setColor(ContextCompat.getColor(this, R.color.deep_blue));
                deepBlue.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.light_blue -> {
                paint.setColor(ContextCompat.getColor(this, R.color.light_blue));
                lightBlue.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.deep_purple -> {
                paint.setColor(ContextCompat.getColor(this, R.color.deep_purple));
                deepPurple.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.light_purple -> {
                paint.setColor(ContextCompat.getColor(this, R.color.light_purple));
                lightPurple.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.deep_green -> {
                paint.setColor(ContextCompat.getColor(this, R.color.deep_green));
                deepGreen.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.light_green -> {
                paint.setColor(ContextCompat.getColor(this, R.color.light_green));
                lightGreen.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.deep_pink -> {
                paint.setColor(ContextCompat.getColor(this, R.color.deep_pink));
                deepPink.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.light_pink -> {
                paint.setColor(ContextCompat.getColor(this, R.color.light_pink));
                lightPink.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.red -> {
                paint.setColor(ContextCompat.getColor(this, R.color.red));
                Red.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.deep_orange -> {
                paint.setColor(ContextCompat.getColor(this, R.color.deep_orange));
                deepOrange.setSelected(true);
                updateMyViewPaint();
            }
            case R.id.select_color -> {
                ColorPickerDialogBuilder
                        .with(this)
                        .setTitle(getString(R.string.chooseColor))
                        .wheelType(ColorPickerView.WHEEL_TYPE.FLOWER)
                        .density(12)
                        .showLightnessSlider(false)
                        .setOnColorSelectedListener(new OnColorSelectedListener() {
                            @Override
                            public void onColorSelected(int selectedColor) {
                                // منطق إضافي إذا لزم
                            }
                        })
                        .setPositiveButton(getString(R.string.ok), new ColorPickerClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int selectedColor, Integer[] allColors) {
                                paint.setColor(selectedColor);
                                updateMyViewPaint();
                            }
                        })
                        .setNegativeButton(getString(R.string.cancel), new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) { }
                        })
                        .build()
                        .show();
            }

        }
    }

    private void enable() {
        White.setSelected(false);
        Black.setSelected(false);
        Gray.setSelected(false);
        lightOrange.setSelected(false);
        Brown.setSelected(false);
        Yellow.setSelected(false);
        deepBlue.setSelected(false);
        lightBlue.setSelected(false);
        deepPurple.setSelected(false);
        lightPurple.setSelected(false);
        deepGreen.setSelected(false);
        lightGreen.setSelected(false);
        deepPink.setSelected(false);
        lightPink.setSelected(false);
        Red.setSelected(false);
        deepOrange.setSelected(false);
    }

    private void requestPermission() {
        if (ActivityCompat.shouldShowRequestPermissionRationale(MainActivity.this, Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
            Toast.makeText(MainActivity.this, getString(R.string.permission), Toast.LENGTH_LONG).show();
        } else {
            ActivityCompat.requestPermissions(MainActivity.this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, PERMISSION_REQUEST_CODE);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // التحقق من إعداد الموسيقى الخلفية قبل التشغيل
        boolean backgroundMusicEnabled = preferences.getBoolean("background_music", true);
        if (backgroundMusicEnabled) {
            if (mPlayer != null && !mPlayer.isPlaying()) {
                mPlayer.start();
                mPlayer.setLooping(true);
                Log.d("MainActivity", "Background music resumed - background_music enabled: " + backgroundMusicEnabled);
            } else {
                int backgroundResourceId = this.getResources().getIdentifier("background_" + randomNumber, "raw", this.getPackageName());
                mPlayer = MediaPlayer.create(MainActivity.this, backgroundResourceId);
                mPlayer.start();
                mPlayer.setLooping(true);
                Log.d("MainActivity", "Background music started - background_music enabled: " + backgroundMusicEnabled);
            }
        } else {
            Log.d("MainActivity", "Background music skipped - background_music disabled");
        }

        // التحقق من إعداد الحفظ التلقائي
        checkAutoSaveAndSave();
    }

    @Override
    protected void onPause() {
        super.onPause();
        readyForReview = true;
        if (mPlayer != null && mPlayer.isPlaying()) {
            mPlayer.pause();
        }
    }

    /**
     * تطبيق الوضع الليلي إذا كان مفعلاً في الإعدادات
     */
    private void applyDarkModeIfEnabled() {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(this);
        boolean darkModeEnabled = preferences.getBoolean("dark_mode", false);

        if (darkModeEnabled) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }
    }

    /**
     * تحديث أيقونة المفضلة في القائمة
     */
    private void updateFavoriteMenuItem(Menu menu) {
        MenuItem favoriteItem = menu.findItem(R.id.action_favorite);
        if (favoriteItem != null) {
            String imageKey = "image_" + code + "_" + position;
            boolean isFavorite = favoritesManager.isFavorite(imageKey);

            if (isFavorite) {
                favoriteItem.setIcon(R.drawable.ic_favorite_filled);
                favoriteItem.setTitle(getString(R.string.remove_from_favorites));
            } else {
                favoriteItem.setIcon(R.drawable.ic_favorite_border);
                favoriteItem.setTitle(getString(R.string.add_to_favorites));
            }
        }
    }

    /**
     * تبديل حالة المفضلة للصورة الحالية
     */
    private void toggleFavorite(MenuItem item) {
        String imageKey = "image_" + code + "_" + position;
        boolean isFavorite = favoritesManager.isFavorite(imageKey);

        if (isFavorite) {
            favoritesManager.removeFavorite(imageKey);
            item.setIcon(R.drawable.ic_favorite_border);
            item.setTitle(getString(R.string.add_to_favorites));
            Toast.makeText(this, getString(R.string.removed_from_favorites), Toast.LENGTH_SHORT).show();
        } else {
            // إنشاء كائن ImageItem للصورة الحالية
            ImageItem imageItem = new ImageItem();
            imageItem.setImageName("image_" + position);
            imageItem.setCategoryCode(code);
            imageItem.setPosition(position);

            favoritesManager.addFavorite(imageKey, imageItem);
            item.setIcon(R.drawable.ic_favorite_filled);
            item.setTitle(getString(R.string.remove_from_favorites));
            Toast.makeText(this, getString(R.string.added_to_favorites), Toast.LENGTH_SHORT).show();
        }
    }



    /**
     * التحقق من إعداد الحفظ التلقائي وحفظ الصورة إذا لزم الأمر
     */
    private void checkAutoSaveAndSave() {
        boolean autoSaveEnabled = preferences.getBoolean("auto_save", false);
        if (autoSaveEnabled && myView != null && myView.scaledBitmap != null) {
            // حفظ الصورة تلقائياً
            saveImageToGallery();
            Log.d("MainActivity", "Auto-save completed");
        }
    }


}