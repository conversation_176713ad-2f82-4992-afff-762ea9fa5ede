<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- Share Button with Modern Design -->
    <item
        android:id="@+id/action_share"
        android:clickable="true"
        android:icon="@drawable/share_icon"
        android:title="@string/action_share"
        app:showAsAction="always"
        app:iconTint="@color/on_surface_primary" />

    <!-- Save Button with Modern Design -->
    <item
        android:id="@+id/action_save"
        android:clickable="true"
        android:icon="@drawable/save_icon"
        android:title="@string/action_save"
        app:showAsAction="always"
        app:iconTint="@color/on_surface_primary" />

    <!-- Sound Toggle Button with Modern Design -->
    <item
        android:id="@+id/action_mute"
        android:clickable="true"
        android:icon="@drawable/sound_on"
        android:title="@string/action_mute"
        app:showAsAction="always"
        app:iconTint="@color/on_surface_primary" />

    <!-- Undo <PERSON><PERSON> with Modern Design -->
    <item
        android:id="@+id/action_undo"
        android:clickable="true"
        android:icon="@drawable/undo_icon"
        android:title="@string/action_undo"
        app:showAsAction="always"
        app:iconTint="@color/on_surface_primary" />

    <!-- Favorite Button with Modern Design -->
    <item
        android:id="@+id/action_favorite"
        android:clickable="true"
        android:icon="@drawable/ic_favorite_border"
        android:title="@string/add_to_favorites"
        app:showAsAction="always"
        app:iconTint="@color/on_surface_primary" />


</menu>