<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="320dp"
    android:height="320dp"
    android:viewportWidth="320"
    android:viewportHeight="320">
    
    <!-- Geometric Pattern for Navigation Header -->
    <group android:name="nav_pattern">
        <!-- Large Circles -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M80,80 A30,30 0 1,1 80,140 A30,30 0 1,1 80,80 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M240,80 A25,25 0 1,1 240,130 A25,25 0 1,1 240,80 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M160,240 A35,35 0 1,1 160,310 A35,35 0 1,1 160,240 Z" />
            
        <!-- Medium Circles -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M40,200 A20,20 0 1,1 40,240 A20,20 0 1,1 40,200 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M280,180 A18,18 0 1,1 280,216 A18,18 0 1,1 280,180 Z" />
            
        <!-- Small Circles -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M120,40 A12,12 0 1,1 120,64 A12,12 0 1,1 120,40 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M200,280 A15,15 0 1,1 200,310 A15,15 0 1,1 200,280 Z" />
            
        <!-- Triangular Elements -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M30,60 L50,60 L40,80 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M270,40 L290,40 L280,60 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M50,280 L70,280 L60,300 Z" />
            
        <!-- Diamond Shapes -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M140,20 L150,30 L140,40 L130,30 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M180,300 L190,310 L180,320 L170,310 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M300,120 L310,130 L300,140 L290,130 Z" />
            
        <!-- Star Elements -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M20,120 L25,110 L30,120 L25,130 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M290,200 L295,190 L300,200 L295,210 Z" />
    </group>
</vector>
