<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:layoutDirection="rtl"
    android:orientation="vertical">

    <!-- Color Palette Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_margin="5dp"
        android:gravity="center_horizontal"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <!-- First Row of Colors -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/deep_orange"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_deep_orange"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/light_pink"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_light_pink"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/light_green"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_light_green"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/yellow"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_yellow"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/light_blue"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_light_blue"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/deep_purple"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_deep_purple"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/light_orange"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_light_orange"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/white"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_white"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />
            </LinearLayout>

            <!-- Second Row of Colors -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/red"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_red"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/deep_pink"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_deep_pink"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/deep_green"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_deep_green"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/light_purple"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_light_purple"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/deep_blue"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_deep_blue"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/brown"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_brown"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/gray"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_gray"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />

                <ImageButton
                    android:id="@+id/black"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="2dp"
                    android:background="@drawable/color_black"
                    android:scaleType="fitXY"
                    android:contentDescription="Color Button" />
            </LinearLayout>
        </LinearLayout>

        <!-- Color Selector Button -->
        <ImageButton
            android:id="@+id/select_color"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_margin="2dp"
            android:background="@drawable/select_color" />

    </LinearLayout>

    <!-- Spacer -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:visibility="visible" />

    <!-- Canvas Area -->
    <RelativeLayout
        android:id="@+id/relative_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="10dp"
        android:layout_gravity="center"
        android:gravity="center"
        android:splitMotionEvents="true">

        <ImageView
            android:id="@+id/coringImage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center_horizontal|center_vertical"
            android:visibility="gone" />
    </RelativeLayout>

    <!-- Bottom Ad Banner -->
    <com.google.android.gms.ads.AdView
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:background="@android:color/white"
        android:elevation="4dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id"/>

</LinearLayout>
