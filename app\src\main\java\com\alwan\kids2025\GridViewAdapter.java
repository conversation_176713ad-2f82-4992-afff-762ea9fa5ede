package com.alwan.kids2025;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.Toast;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;

import java.util.ArrayList;

/**
 * Created by AHED.OMN on 04/01/2025.
 * <EMAIL>
 */

public class GridViewAdapter extends ArrayAdapter<ImageItem> {
    private Context context;
    private int layoutResourceId;
    private ArrayList<ImageItem> data = new ArrayList<>();
    private FavoritesManager favoritesManager;
    private int categoryCode;

    public GridViewAdapter(Context context, int layoutResourceId, ArrayList<ImageItem> data, int categoryCode) {
        super(context, layoutResourceId, data);
        this.layoutResourceId = layoutResourceId;
        this.context = context;
        this.data = data;
        this.categoryCode = categoryCode;
        this.favoritesManager = new FavoritesManager(context);
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        View row = convertView;
        ViewHolder holder = null;

        if (row == null) {
            LayoutInflater inflater = ((Activity) context).getLayoutInflater();
            row = inflater.inflate(layoutResourceId, parent, false);
            holder = new ViewHolder();
            holder.image = (ImageView) row.findViewById(R.id.image);
            row.setTag(holder);
        } else {
            holder = (ViewHolder) row.getTag();
        }

        ImageItem item = (ImageItem) data.get(position);
        holder.image.setImageBitmap(item.getImage());

        // إعداد النقر على الصورة لفتحها
        setupImageClick(holder, position);

        return row;
    }

    private void setupImageClick(ViewHolder holder, int position) {
        // إعداد النقر على الصورة لفتحها في MainActivityClean
        holder.image.setOnClickListener(v -> {
            try {
                Log.d("GridViewAdapter", "Image clicked at position: " + position);

                // إنشاء Intent لفتح MainActivityClean
                Intent intent = new Intent(context, MainActivityClean.class);
                intent.putExtra("code", categoryCode);
                intent.putExtra("position", position + 1); // position + 1 لأن الصور تبدأ من 1
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

                Log.d("GridViewAdapter", "Starting MainActivityClean with code: " + categoryCode + ", position: " + (position + 1));
                context.startActivity(intent);

            } catch (Exception e) {
                Log.e("GridViewAdapter", "Error opening image: " + e.getMessage());
                Toast.makeText(context, "خطأ في فتح الصورة", Toast.LENGTH_SHORT).show();
            }
        });
    }

    static class ViewHolder {
        ImageView image;
    }
}