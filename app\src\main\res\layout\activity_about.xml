<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modern_background">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/modern_gradient_background"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="70dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- App Logo Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="24dp"
                app:cardElevation="12dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/modern_gradient_background"
                    android:orientation="vertical"
                    android:padding="32dp"
                    android:gravity="center">

                    <!-- App Logo -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_marginBottom="16dp"
                        app:cardCornerRadius="60dp"
                        app:cardElevation="16dp"
                        app:cardBackgroundColor="@android:color/white">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="20dp"
                            android:src="@drawable/small_logo"
                            android:scaleType="centerInside" />

                    </androidx.cardview.widget.CardView>

                    <!-- App Name -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/app_name"
                        android:textColor="@android:color/white"
                        android:textSize="28sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="8dp" />

                    <!-- Version -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/app_version_about"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:fontFamily="@font/blabeloo"
                        android:alpha="0.9" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Description Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/app_description_title"
                        android:textColor="@color/modern_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/app_description_text"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:fontFamily="@font/blabeloo"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Features Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/app_features_title"
                        android:textColor="@color/modern_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="16dp" />

                    <!-- Feature Items -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- Feature 1 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_marginEnd="12dp"
                                android:src="@drawable/ic_flowers_modern"
                                android:tint="@color/flowers_color" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/feature_6_categories"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                        <!-- Feature 2 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_marginEnd="12dp"
                                android:src="@drawable/ic_cartoons_modern"
                                android:tint="@color/cartoons_color" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/feature_child_friendly"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                        <!-- Feature 3 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_marginEnd="12dp"
                                android:src="@drawable/ic_animals_modern"
                                android:tint="@color/animals_color" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/feature_save_share"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                        <!-- Feature 4 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_marginEnd="12dp"
                                android:src="@drawable/ic_star_modern"
                                android:tint="@color/modern_accent" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/feature_modern_design"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Developer Info -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/developer_info_title"
                        android:textColor="@color/modern_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/developer_info_text"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:fontFamily="@font/blabeloo"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Navigation -->
    <include
        layout="@layout/simple_bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
