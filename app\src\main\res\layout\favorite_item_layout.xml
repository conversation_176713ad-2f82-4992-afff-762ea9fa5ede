<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp"
    app:cardBackgroundColor="@android:color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="180dp">

        <!-- صورة المفضلة -->
        <ImageView
            android:id="@+id/favorite_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="4dp"
            android:scaleType="centerCrop"
            android:background="@drawable/border"
            android:src="@drawable/gp1_1" />

        <!-- زر إزالة المفضلة -->
        <androidx.cardview.widget.CardView
            android:id="@+id/favorite_remove_card"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_margin="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/red">

            <ImageView
                android:id="@+id/favorite_remove_icon"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_favorite_filled"
                android:tint="@android:color/white"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true" />

        </androidx.cardview.widget.CardView>

        <!-- تأثير التدرج في الأسفل -->
        <View
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/gradient_overlay"
            android:alpha="0.7" />

    </RelativeLayout>

</androidx.cardview.widget.CardView>
