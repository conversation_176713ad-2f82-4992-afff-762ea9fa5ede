1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.alwan.kids2025"
4    android:versionCode="4"
5    android:versionName="4.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->Z:\alwan6\app\src\main\AndroidManifest.xml:5:5-67
11-->Z:\alwan6\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission
12-->Z:\alwan6\app\src\main\AndroidManifest.xml:6:5-7:38
13        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
13-->Z:\alwan6\app\src\main\AndroidManifest.xml:6:22-78
14        android:maxSdkVersion="28" />
14-->Z:\alwan6\app\src\main\AndroidManifest.xml:7:9-35
15    <uses-permission
15-->Z:\alwan6\app\src\main\AndroidManifest.xml:8:5-9:38
16        android:name="android.permission.READ_EXTERNAL_STORAGE"
16-->Z:\alwan6\app\src\main\AndroidManifest.xml:8:22-77
17        android:maxSdkVersion="32" />
17-->Z:\alwan6\app\src\main\AndroidManifest.xml:9:9-35
18    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
18-->Z:\alwan6\app\src\main\AndroidManifest.xml:10:5-76
18-->Z:\alwan6\app\src\main\AndroidManifest.xml:10:22-74
19    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
19-->Z:\alwan6\app\src\main\AndroidManifest.xml:11:5-79
19-->Z:\alwan6\app\src\main\AndroidManifest.xml:11:22-76
20
21    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
22    <permission
22-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:7:5-9:47
23        android:name="com.alwan.kids2025.permission.C2D_MESSAGE"
23-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:8:9-63
24        android:protectionLevel="signature" />
24-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:9:9-44
25
26    <uses-permission android:name="com.alwan.kids2025.permission.C2D_MESSAGE" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
26-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:5-79
26-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:22-76
27    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- Required, makes sure notifications are delivered on time. -->
27-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:18:5-82
27-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:18:22-79
28    <uses-permission android:name="android.permission.WAKE_LOCK" />
28-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:19:5-68
28-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:19:22-65
29    <!--
30 Required so the device vibrates on receiving a push notification.
31         Vibration settings of the device still apply.
32    -->
33    <uses-permission android:name="android.permission.VIBRATE" />
33-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:24:5-66
33-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:24:22-63
34    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
34-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:25:5-79
34-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:25:22-76
35    <!--
36 Use to restore notifications the user hasn't interacted with.
37         They could be missed notifications if the user reboots their device if this isn't in place.
38    -->
39    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- START: ShortcutBadger -->
39-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:30:5-81
39-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:30:22-78
40    <!-- Samsung -->
41    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
41-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:5-86
41-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:22-83
42    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
42-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:5-87
42-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:22-84
43    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
43-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:5-81
43-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:22-78
44    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
44-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:5-83
44-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:22-80
45    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
45-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:5-88
45-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:22-85
46    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
46-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:5-92
46-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:22-89
47    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
47-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:5-84
47-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:22-81
48    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
48-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:5-83
48-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:22-80
49    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
49-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:5-91
49-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:22-88
50    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
50-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:5-92
50-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:22-89
51    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
51-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:5-93
51-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:22-90
52    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
52-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:5-73
52-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:22-70
53    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
53-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:5-82
53-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:22-79
54    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
54-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:5-83
54-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:22-80
55    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
55-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:5-88
55-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:22-85
56    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
56-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:5-89
56-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:22-86
57    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
57-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:27:5-82
57-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:27:22-79
58    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
58-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:28:5-88
58-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:28:22-85
59    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
59-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:5-83
59-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:22-80
60    <queries>
60-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:35:5-68:15
61
62        <!-- For browser content -->
63        <intent>
63-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:38:9-44:18
64            <action android:name="android.intent.action.VIEW" />
64-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
64-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
65
66            <category android:name="android.intent.category.BROWSABLE" />
66-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:13-74
66-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:23-71
67
68            <data android:scheme="https" />
68-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
68-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
69        </intent>
70        <!-- End of browser content -->
71        <!-- For CustomTabsService -->
72        <intent>
72-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:47:9-49:18
73            <action android:name="android.support.customtabs.action.CustomTabsService" />
73-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:13-90
73-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:21-87
74        </intent>
75        <!-- End of CustomTabsService -->
76        <!-- For MRAID capabilities -->
77        <intent>
77-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:52:9-56:18
78            <action android:name="android.intent.action.INSERT" />
78-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:13-67
78-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:21-64
79
80            <data android:mimeType="vnd.android.cursor.dir/event" />
80-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
81        </intent>
82        <intent>
82-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:57:9-61:18
83            <action android:name="android.intent.action.VIEW" />
83-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
83-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
84
85            <data android:scheme="sms" />
85-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
85-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
86        </intent>
87        <intent>
87-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:62:9-66:18
88            <action android:name="android.intent.action.DIAL" />
88-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:13-65
88-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:21-62
89
90            <data android:path="tel:" />
90-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
91        </intent>
92        <!-- End of MRAID capabilities -->
93    </queries> <!-- Permission will be merged into the manifest of the hosting app. -->
94    <!-- Is required to launch foreground extraction service for targetSdkVersion 28+. -->
95    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- Is required to launch foreground extraction service for targetSdkVersion 34+. -->
95-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:14:5-77
95-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:14:22-74
96    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
96-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:16:5-87
96-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:16:22-84
97    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
97-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
97-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
98
99    <permission
99-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
100        android:name="com.alwan.kids2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
100-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
101        android:protectionLevel="signature" />
101-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
102
103    <uses-permission android:name="com.alwan.kids2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
103-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
103-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
104
105    <application
105-->Z:\alwan6\app\src\main\AndroidManifest.xml:14:5-98:19
106        android:allowBackup="false"
106-->Z:\alwan6\app\src\main\AndroidManifest.xml:15:9-36
107        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
107-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
108        android:debuggable="true"
109        android:extractNativeLibs="true"
110        android:icon="@drawable/logo"
110-->Z:\alwan6\app\src\main\AndroidManifest.xml:16:9-38
111        android:label="@string/app_name"
111-->Z:\alwan6\app\src\main\AndroidManifest.xml:17:9-41
112        android:largeHeap="true"
112-->Z:\alwan6\app\src\main\AndroidManifest.xml:19:9-33
113        android:requestLegacyExternalStorage="true"
113-->Z:\alwan6\app\src\main\AndroidManifest.xml:21:9-52
114        android:supportsRtl="true"
114-->Z:\alwan6\app\src\main\AndroidManifest.xml:18:9-35
115        android:theme="@style/AppTheme" >
115-->Z:\alwan6\app\src\main\AndroidManifest.xml:20:9-40
116        <meta-data
116-->Z:\alwan6\app\src\main\AndroidManifest.xml:22:9-100
117            android:name="google_analytics_adid_collection_enabled"
117-->Z:\alwan6\app\src\main\AndroidManifest.xml:22:20-75
118            android:value="false" />
118-->Z:\alwan6\app\src\main\AndroidManifest.xml:22:76-97
119        <!-- Removed duplicate OneSignal App ID meta-data entry -->
120
121        <meta-data
121-->Z:\alwan6\app\src\main\AndroidManifest.xml:25:9-27:69
122            android:name="com.google.android.gms.ads.APPLICATION_ID"
122-->Z:\alwan6\app\src\main\AndroidManifest.xml:26:13-69
123            android:value="ca-app-pub-7841751633097845~3195890380" />
123-->Z:\alwan6\app\src\main\AndroidManifest.xml:27:13-67
124
125        <property
125-->Z:\alwan6\app\src\main\AndroidManifest.xml:28:9-32:48
126            android:name="android.adservices.AD_SERVICES_CONFIG"
126-->Z:\alwan6\app\src\main\AndroidManifest.xml:30:13-65
127            android:resource="@xml/gma_ad_services_config" />
127-->Z:\alwan6\app\src\main\AndroidManifest.xml:31:13-59
128
129        <activity
129-->Z:\alwan6\app\src\main\AndroidManifest.xml:33:9-36:59
130            android:name="com.alwan.kids2025.MainActivityClean"
130-->Z:\alwan6\app\src\main\AndroidManifest.xml:34:13-64
131            android:exported="true"
131-->Z:\alwan6\app\src\main\AndroidManifest.xml:35:13-36
132            android:theme="@style/AppTheme.NoActionBar" />
132-->Z:\alwan6\app\src\main\AndroidManifest.xml:36:13-56
133
134        <!-- Keep Categories for backward compatibility -->
135        <activity
135-->Z:\alwan6\app\src\main\AndroidManifest.xml:41:9-44:40
136            android:name="com.alwan.kids2025.Categories"
136-->Z:\alwan6\app\src\main\AndroidManifest.xml:42:13-57
137            android:exported="false"
137-->Z:\alwan6\app\src\main\AndroidManifest.xml:44:13-37
138            android:theme="@style/AppTheme.NoActionBar" />
138-->Z:\alwan6\app\src\main\AndroidManifest.xml:43:13-56
139        <activity
139-->Z:\alwan6\app\src\main\AndroidManifest.xml:45:9-47:59
140            android:name="com.alwan.kids2025.CategoryItems"
140-->Z:\alwan6\app\src\main\AndroidManifest.xml:46:13-60
141            android:theme="@style/AppTheme.NoActionBar" />
141-->Z:\alwan6\app\src\main\AndroidManifest.xml:47:13-56
142        <activity
142-->Z:\alwan6\app\src\main\AndroidManifest.xml:48:9-57:20
143            android:name="com.alwan.kids2025.Splash"
143-->Z:\alwan6\app\src\main\AndroidManifest.xml:50:13-53
144            android:exported="true"
144-->Z:\alwan6\app\src\main\AndroidManifest.xml:49:13-36
145            android:theme="@style/AppTheme.NoActionBar" >
145-->Z:\alwan6\app\src\main\AndroidManifest.xml:51:13-56
146            <intent-filter>
146-->Z:\alwan6\app\src\main\AndroidManifest.xml:52:13-56:29
147                <action android:name="android.intent.action.MAIN" />
147-->Z:\alwan6\app\src\main\AndroidManifest.xml:53:17-69
147-->Z:\alwan6\app\src\main\AndroidManifest.xml:53:25-66
148
149                <category android:name="android.intent.category.LAUNCHER" />
149-->Z:\alwan6\app\src\main\AndroidManifest.xml:55:17-77
149-->Z:\alwan6\app\src\main\AndroidManifest.xml:55:27-74
150            </intent-filter>
151        </activity>
152
153        <!-- New Activities -->
154        <activity
154-->Z:\alwan6\app\src\main\AndroidManifest.xml:60:9-63:74
155            android:name="com.alwan.kids2025.AboutActivity"
155-->Z:\alwan6\app\src\main\AndroidManifest.xml:61:13-60
156            android:parentActivityName="com.alwan.kids2025.Categories"
156-->Z:\alwan6\app\src\main\AndroidManifest.xml:63:13-71
157            android:theme="@style/AppTheme.NoActionBar" />
157-->Z:\alwan6\app\src\main\AndroidManifest.xml:62:13-56
158        <activity
158-->Z:\alwan6\app\src\main\AndroidManifest.xml:64:9-67:74
159            android:name="com.alwan.kids2025.SettingsActivity"
159-->Z:\alwan6\app\src\main\AndroidManifest.xml:65:13-63
160            android:parentActivityName="com.alwan.kids2025.Categories"
160-->Z:\alwan6\app\src\main\AndroidManifest.xml:67:13-71
161            android:theme="@style/AppTheme.NoActionBar" />
161-->Z:\alwan6\app\src\main\AndroidManifest.xml:66:13-56
162        <activity
162-->Z:\alwan6\app\src\main\AndroidManifest.xml:69:9-72:74
163            android:name="com.alwan.kids2025.FavoritesActivity"
163-->Z:\alwan6\app\src\main\AndroidManifest.xml:70:13-64
164            android:parentActivityName="com.alwan.kids2025.Categories"
164-->Z:\alwan6\app\src\main\AndroidManifest.xml:72:13-71
165            android:theme="@style/AppTheme.NoActionBar" />
165-->Z:\alwan6\app\src\main\AndroidManifest.xml:71:13-56
166        <activity
166-->Z:\alwan6\app\src\main\AndroidManifest.xml:73:9-76:74
167            android:name="com.alwan.kids2025.MoreActivity"
167-->Z:\alwan6\app\src\main\AndroidManifest.xml:74:13-59
168            android:parentActivityName="com.alwan.kids2025.Categories"
168-->Z:\alwan6\app\src\main\AndroidManifest.xml:76:13-71
169            android:theme="@style/AppTheme.NoActionBar" />
169-->Z:\alwan6\app\src\main\AndroidManifest.xml:75:13-56
170
171        <service
171-->Z:\alwan6\app\src\main\AndroidManifest.xml:79:9-86:19
172            android:name="com.alwan.kids2025.MyFirebaseMessagingService"
172-->Z:\alwan6\app\src\main\AndroidManifest.xml:80:13-73
173            android:enabled="true"
173-->Z:\alwan6\app\src\main\AndroidManifest.xml:81:13-35
174            android:exported="true" >
174-->Z:\alwan6\app\src\main\AndroidManifest.xml:82:13-36
175            <intent-filter>
175-->Z:\alwan6\app\src\main\AndroidManifest.xml:83:13-85:29
176                <action android:name="com.google.firebase.MESSAGING_EVENT" />
176-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:17-78
176-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:25-75
177            </intent-filter>
178        </service>
179
180        <!-- FileProvider for sharing images -->
181        <provider
182            android:name="androidx.core.content.FileProvider"
182-->Z:\alwan6\app\src\main\AndroidManifest.xml:90:13-62
183            android:authorities="com.alwan.kids2025.fileprovider"
183-->Z:\alwan6\app\src\main\AndroidManifest.xml:91:13-64
184            android:exported="false"
184-->Z:\alwan6\app\src\main\AndroidManifest.xml:92:13-37
185            android:grantUriPermissions="true" >
185-->Z:\alwan6\app\src\main\AndroidManifest.xml:93:13-47
186            <meta-data
186-->Z:\alwan6\app\src\main\AndroidManifest.xml:94:13-96:54
187                android:name="android.support.FILE_PROVIDER_PATHS"
187-->Z:\alwan6\app\src\main\AndroidManifest.xml:95:17-67
188                android:resource="@xml/file_paths" />
188-->Z:\alwan6\app\src\main\AndroidManifest.xml:96:17-51
189        </provider>
190
191        <receiver
191-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:50:9-61:20
192            android:name="com.onesignal.FCMBroadcastReceiver"
192-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:51:13-62
193            android:exported="true"
193-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:52:13-36
194            android:permission="com.google.android.c2dm.permission.SEND" >
194-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:53:13-73
195
196            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
197            <intent-filter android:priority="999" >
197-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:13-60:29
197-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:28-50
198                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
198-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
198-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
199
200                <category android:name="com.alwan.kids2025" />
200-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:17-61
200-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:27-58
201            </intent-filter>
202        </receiver>
203
204        <service
204-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:63:9-69:19
205            android:name="com.onesignal.HmsMessageServiceOneSignal"
205-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:64:13-68
206            android:exported="false" >
206-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:65:13-37
207            <intent-filter>
207-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:66:13-68:29
208                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
208-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:17-81
208-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:25-78
209            </intent-filter>
210        </service>
211
212        <activity
212-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:71:9-79:20
213            android:name="com.onesignal.NotificationOpenedActivityHMS"
213-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:72:13-71
214            android:exported="true"
214-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:73:13-36
215            android:noHistory="true"
215-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:74:13-37
216            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
216-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:75:13-72
217            <intent-filter>
217-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:76:13-78:29
218                <action android:name="android.intent.action.VIEW" />
218-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
218-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
219            </intent-filter>
220        </activity>
221
222        <service
222-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:81:9-83:40
223            android:name="com.onesignal.FCMIntentService"
223-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:82:13-58
224            android:exported="false" />
224-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:83:13-37
225        <service
225-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:84:9-87:72
226            android:name="com.onesignal.FCMIntentJobService"
226-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:85:13-61
227            android:exported="false"
227-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:86:13-37
228            android:permission="android.permission.BIND_JOB_SERVICE" />
228-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:87:13-69
229        <service
229-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:88:9-91:43
230            android:name="com.onesignal.SyncService"
230-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:89:13-53
231            android:exported="false"
231-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:90:13-37
232            android:stopWithTask="true" />
232-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:91:13-40
233        <service
233-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:92:9-95:72
234            android:name="com.onesignal.SyncJobService"
234-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:93:13-56
235            android:exported="false"
235-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:94:13-37
236            android:permission="android.permission.BIND_JOB_SERVICE" />
236-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:95:13-69
237
238        <activity
238-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:97:9-100:75
239            android:name="com.onesignal.PermissionsActivity"
239-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:98:13-61
240            android:exported="false"
240-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:99:13-37
241            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
241-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:100:13-72
242
243        <receiver
243-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:102:9-104:39
244            android:name="com.onesignal.NotificationDismissReceiver"
244-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:103:13-69
245            android:exported="true" />
245-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:104:13-36
246        <receiver
246-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:105:9-112:20
247            android:name="com.onesignal.BootUpReceiver"
247-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:106:13-56
248            android:exported="true" >
248-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:107:13-36
249            <intent-filter>
249-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:108:13-111:29
250                <action android:name="android.intent.action.BOOT_COMPLETED" />
250-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
250-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
251                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
251-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:17-82
251-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:25-79
252            </intent-filter>
253        </receiver>
254        <receiver
254-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:113:9-119:20
255            android:name="com.onesignal.UpgradeReceiver"
255-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:114:13-57
256            android:exported="true" >
256-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:115:13-36
257            <intent-filter>
257-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:116:13-118:29
258                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
258-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:17-84
258-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:25-81
259            </intent-filter>
260        </receiver>
261
262        <activity
262-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:121:9-127:75
263            android:name="com.onesignal.NotificationOpenedReceiver"
263-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:122:13-68
264            android:excludeFromRecents="true"
264-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:123:13-46
265            android:exported="true"
265-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:124:13-36
266            android:noHistory="true"
266-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:125:13-37
267            android:taskAffinity=""
267-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:126:13-36
268            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
268-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:127:13-72
269        <activity
269-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:128:9-133:75
270            android:name="com.onesignal.NotificationOpenedReceiverAndroid22AndOlder"
270-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:129:13-85
271            android:excludeFromRecents="true"
271-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:130:13-46
272            android:exported="true"
272-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:131:13-36
273            android:noHistory="true"
273-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:132:13-37
274            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
274-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:133:13-72
275        <activity
275-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:73:9-78:43
276            android:name="com.google.android.gms.ads.AdActivity"
276-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:74:13-65
277            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
277-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:75:13-122
278            android:exported="false"
278-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:76:13-37
279            android:theme="@android:style/Theme.Translucent" />
279-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:77:13-61
280
281        <provider
281-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:80:9-85:43
282            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
282-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:81:13-76
283            android:authorities="com.alwan.kids2025.mobileadsinitprovider"
283-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:82:13-73
284            android:exported="false"
284-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:83:13-37
285            android:initOrder="100" />
285-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:84:13-36
286
287        <service
287-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:87:9-91:43
288            android:name="com.google.android.gms.ads.AdService"
288-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:88:13-64
289            android:enabled="true"
289-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:89:13-35
290            android:exported="false" />
290-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:90:13-37
291
292        <activity
292-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:93:9-97:43
293            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
293-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:94:13-82
294            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
294-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:95:13-122
295            android:exported="false" />
295-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:96:13-37
296        <activity
296-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:98:9-105:43
297            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
297-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:99:13-82
298            android:excludeFromRecents="true"
298-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:100:13-46
299            android:exported="false"
299-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:101:13-37
300            android:launchMode="singleTask"
300-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:102:13-44
301            android:taskAffinity=""
301-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:103:13-36
302            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- The services will be merged into the manifest of the hosting app. -->
302-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:104:13-72
303        <service
303-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:21:9-28:19
304            android:name="com.google.android.play.core.assetpacks.AssetPackExtractionService"
304-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:22:13-94
305            android:enabled="false"
305-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:23:13-36
306            android:exported="true" >
306-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:24:13-36
307            <meta-data
307-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:25:13-27:41
308                android:name="com.google.android.play.core.assetpacks.versionCode"
308-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:26:17-83
309                android:value="20300" />
309-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:27:17-38
310        </service>
311        <service
311-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:29:9-33:56
312            android:name="com.google.android.play.core.assetpacks.ExtractionForegroundService"
312-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:30:13-95
313            android:enabled="false"
313-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:31:13-36
314            android:exported="false"
314-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:32:13-37
315            android:foregroundServiceType="dataSync" />
315-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:33:13-53
316
317        <receiver
317-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:35:9-43:20
318            android:name="com.google.android.play.core.assetpacks.SessionStateBroadcastReceiver"
318-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:36:13-97
319            android:enabled="true"
319-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:37:13-35
320            android:exported="true"
320-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:38:13-36
321            android:permission="android.permission.INSTALL_PACKAGES" >
321-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:39:13-69
322            <intent-filter>
322-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:40:13-42:29
323                <action android:name="com.google.android.play.core.assetpacks.receiver.ACTION_SESSION_UPDATE" />
323-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:41:17-113
323-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:41:25-110
324            </intent-filter>
325        </receiver>
326        <receiver
326-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:29:9-40:20
327            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
327-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:30:13-78
328            android:exported="true"
328-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:31:13-36
329            android:permission="com.google.android.c2dm.permission.SEND" >
329-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:32:13-73
330            <intent-filter>
330-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:33:13-35:29
331                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
331-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
331-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
332            </intent-filter>
333
334            <meta-data
334-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:37:13-39:40
335                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
335-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:38:17-92
336                android:value="true" />
336-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:39:17-37
337        </receiver>
338        <!--
339             FirebaseMessagingService performs security checks at runtime,
340             but set to not exported to explicitly avoid allowing another app to call it.
341        -->
342        <service
342-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:46:9-53:19
343            android:name="com.google.firebase.messaging.FirebaseMessagingService"
343-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:47:13-82
344            android:directBootAware="true"
344-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:48:13-43
345            android:exported="false" >
345-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:49:13-37
346            <intent-filter android:priority="-500" >
346-->Z:\alwan6\app\src\main\AndroidManifest.xml:83:13-85:29
347                <action android:name="com.google.firebase.MESSAGING_EVENT" />
347-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:17-78
347-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:25-75
348            </intent-filter>
349        </service>
350        <service
350-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:54:9-63:19
351            android:name="com.google.firebase.components.ComponentDiscoveryService"
351-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:55:13-84
352            android:directBootAware="true"
352-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
353            android:exported="false" >
353-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:56:13-37
354            <meta-data
354-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:57:13-59:85
355                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
355-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:58:17-122
356                android:value="com.google.firebase.components.ComponentRegistrar" />
356-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:59:17-82
357            <meta-data
357-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:60:13-62:85
358                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
358-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:61:17-119
359                android:value="com.google.firebase.components.ComponentRegistrar" />
359-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:62:17-82
360            <meta-data
360-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a6a9eeb64f60b995434706b8f1fbad3\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
361                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
361-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a6a9eeb64f60b995434706b8f1fbad3\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
362                android:value="com.google.firebase.components.ComponentRegistrar" />
362-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a6a9eeb64f60b995434706b8f1fbad3\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
363            <meta-data
363-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
364                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
364-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
365                android:value="com.google.firebase.components.ComponentRegistrar" />
365-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
366            <meta-data
366-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
367                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
367-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
368                android:value="com.google.firebase.components.ComponentRegistrar" />
368-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
369            <meta-data
369-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe74116495f61bbb5439e8eb676f6bb\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
370                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
370-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe74116495f61bbb5439e8eb676f6bb\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
371                android:value="com.google.firebase.components.ComponentRegistrar" />
371-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe74116495f61bbb5439e8eb676f6bb\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
372            <meta-data
372-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
373                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
373-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
374                android:value="com.google.firebase.components.ComponentRegistrar" />
374-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
375            <meta-data
375-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33fb70538b965f1a9670519e671fc5e1\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
376                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
376-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33fb70538b965f1a9670519e671fc5e1\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
377                android:value="com.google.firebase.components.ComponentRegistrar" />
377-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33fb70538b965f1a9670519e671fc5e1\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
378        </service>
379
380        <provider
380-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
381            android:name="com.google.firebase.provider.FirebaseInitProvider"
381-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
382            android:authorities="com.alwan.kids2025.firebaseinitprovider"
382-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
383            android:directBootAware="true"
383-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
384            android:exported="false"
384-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
385            android:initOrder="100" />
385-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
386
387        <receiver
387-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
388            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
388-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
389            android:enabled="true"
389-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
390            android:exported="false" >
390-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
391        </receiver>
392
393        <service
393-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
394            android:name="com.google.android.gms.measurement.AppMeasurementService"
394-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
395            android:enabled="true"
395-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
396            android:exported="false" />
396-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
397        <service
397-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
398            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
398-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
399            android:enabled="true"
399-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
400            android:exported="false"
400-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
401            android:permission="android.permission.BIND_JOB_SERVICE" />
401-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
402
403        <activity
403-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d4e9d128fcd43e5a6e86ce4f6eb5926\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
404            android:name="com.google.android.gms.common.api.GoogleApiActivity"
404-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d4e9d128fcd43e5a6e86ce4f6eb5926\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
405            android:exported="false"
405-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d4e9d128fcd43e5a6e86ce4f6eb5926\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
406            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
406-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d4e9d128fcd43e5a6e86ce4f6eb5926\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
407
408        <provider
408-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:29:9-37:20
409            android:name="androidx.startup.InitializationProvider"
409-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:30:13-67
410            android:authorities="com.alwan.kids2025.androidx-startup"
410-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:31:13-68
411            android:exported="false" >
411-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:32:13-37
412            <meta-data
412-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:34:13-36:52
413                android:name="androidx.work.WorkManagerInitializer"
413-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:35:17-68
414                android:value="androidx.startup" />
414-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:36:17-49
415            <meta-data
415-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d40185c6d6f9ccddea7a3fb92398952c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
416                android:name="androidx.emoji2.text.EmojiCompatInitializer"
416-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d40185c6d6f9ccddea7a3fb92398952c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
417                android:value="androidx.startup" />
417-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d40185c6d6f9ccddea7a3fb92398952c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
418            <meta-data
418-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\472ac2837326f25df4d2cf2853c19477\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
419                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
419-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\472ac2837326f25df4d2cf2853c19477\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
420                android:value="androidx.startup" />
420-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\472ac2837326f25df4d2cf2853c19477\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
421            <meta-data
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
422                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
422-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
423                android:value="androidx.startup" />
423-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
424        </provider>
425
426        <service
426-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
427            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
427-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
428            android:directBootAware="false"
428-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
429            android:enabled="@bool/enable_system_alarm_service_default"
429-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
430            android:exported="false" />
430-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
431        <service
431-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
432            android:name="androidx.work.impl.background.systemjob.SystemJobService"
432-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
433            android:directBootAware="false"
433-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
434            android:enabled="@bool/enable_system_job_service_default"
434-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
435            android:exported="true"
435-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
436            android:permission="android.permission.BIND_JOB_SERVICE" />
436-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
437        <service
437-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
438            android:name="androidx.work.impl.foreground.SystemForegroundService"
438-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
439            android:directBootAware="false"
439-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
440            android:enabled="@bool/enable_system_foreground_service_default"
440-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
441            android:exported="false" />
441-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
442
443        <receiver
443-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
444            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
444-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
445            android:directBootAware="false"
445-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
446            android:enabled="true"
446-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
447            android:exported="false" />
447-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
448        <receiver
448-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
449            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
449-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
450            android:directBootAware="false"
450-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
451            android:enabled="false"
451-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
452            android:exported="false" >
452-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
453            <intent-filter>
453-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
454                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
454-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
454-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
455                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
455-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
455-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
456            </intent-filter>
457        </receiver>
458        <receiver
458-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
459            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
459-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
460            android:directBootAware="false"
460-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
461            android:enabled="false"
461-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
462            android:exported="false" >
462-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
463            <intent-filter>
463-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
464                <action android:name="android.intent.action.BATTERY_OKAY" />
464-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
464-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
465                <action android:name="android.intent.action.BATTERY_LOW" />
465-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
465-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
466            </intent-filter>
467        </receiver>
468        <receiver
468-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
469            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
469-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
470            android:directBootAware="false"
470-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
471            android:enabled="false"
471-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
472            android:exported="false" >
472-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
473            <intent-filter>
473-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
474                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
474-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
474-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
475                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
475-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
475-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
476            </intent-filter>
477        </receiver>
478        <receiver
478-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
479            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
479-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
480            android:directBootAware="false"
480-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
481            android:enabled="false"
481-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
482            android:exported="false" >
482-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
483            <intent-filter>
483-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
484                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
484-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
484-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
485            </intent-filter>
486        </receiver>
487        <receiver
487-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
488            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
488-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
489            android:directBootAware="false"
489-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
490            android:enabled="false"
490-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
491            android:exported="false" >
491-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
492            <intent-filter>
492-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
493                <action android:name="android.intent.action.BOOT_COMPLETED" />
493-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
493-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
494                <action android:name="android.intent.action.TIME_SET" />
494-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
494-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
495                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
495-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
495-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
496            </intent-filter>
497        </receiver>
498        <receiver
498-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
499            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
499-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
500            android:directBootAware="false"
500-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
501            android:enabled="@bool/enable_system_alarm_service_default"
501-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
502            android:exported="false" >
502-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
503            <intent-filter>
503-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
504                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
504-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
504-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
505            </intent-filter>
506        </receiver>
507        <receiver
507-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
508            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
508-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
509            android:directBootAware="false"
509-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
510            android:enabled="true"
510-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
511            android:exported="true"
511-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
512            android:permission="android.permission.DUMP" >
512-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
513            <intent-filter>
513-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
514                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
514-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
514-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
515            </intent-filter>
516        </receiver>
517
518        <uses-library
518-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
519            android:name="androidx.window.extensions"
519-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
520            android:required="false" />
520-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
521        <uses-library
521-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
522            android:name="androidx.window.sidecar"
522-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
523            android:required="false" />
523-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
524        <uses-library
524-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\811dec69550609acf2352cb1e05b0175\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
525            android:name="android.ext.adservices"
525-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\811dec69550609acf2352cb1e05b0175\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
526            android:required="false" />
526-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\811dec69550609acf2352cb1e05b0175\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
527
528        <meta-data
528-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c41e8149bb8e825c8201b1dfddeb0e8d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
529            android:name="com.google.android.gms.version"
529-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c41e8149bb8e825c8201b1dfddeb0e8d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
530            android:value="@integer/google_play_services_version" />
530-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c41e8149bb8e825c8201b1dfddeb0e8d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
531
532        <service
532-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77f8cb0a36d0c54e3a8e1088e63c47af\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
533            android:name="androidx.room.MultiInstanceInvalidationService"
533-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77f8cb0a36d0c54e3a8e1088e63c47af\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
534            android:directBootAware="true"
534-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77f8cb0a36d0c54e3a8e1088e63c47af\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
535            android:exported="false" />
535-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77f8cb0a36d0c54e3a8e1088e63c47af\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
536
537        <receiver
537-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
538            android:name="androidx.profileinstaller.ProfileInstallReceiver"
538-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
539            android:directBootAware="false"
539-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
540            android:enabled="true"
540-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
541            android:exported="true"
541-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
542            android:permission="android.permission.DUMP" >
542-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
543            <intent-filter>
543-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
544                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
544-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
544-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
545            </intent-filter>
546            <intent-filter>
546-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
547                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
548            </intent-filter>
549            <intent-filter>
549-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
550                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
551            </intent-filter>
552            <intent-filter>
552-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
553                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
554            </intent-filter>
555        </receiver>
556
557        <service
557-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
558            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
558-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
559            android:exported="false" >
559-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
560            <meta-data
560-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
561                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
561-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
562                android:value="cct" />
562-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
563        </service>
564        <service
564-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
565            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
565-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
566            android:exported="false"
566-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
567            android:permission="android.permission.BIND_JOB_SERVICE" >
567-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
568        </service>
569
570        <receiver
570-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
571            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
571-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
572            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
572-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
573        <activity
573-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\07b3b08723cfc757f0a46648ed4d6b31\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
574            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
574-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\07b3b08723cfc757f0a46648ed4d6b31\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
575            android:exported="false"
575-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\07b3b08723cfc757f0a46648ed4d6b31\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
576            android:stateNotNeeded="true"
576-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\07b3b08723cfc757f0a46648ed4d6b31\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
577            android:theme="@style/Theme.PlayCore.Transparent" />
577-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\07b3b08723cfc757f0a46648ed4d6b31\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
578    </application>
579
580</manifest>
