1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.alwan.kids2025"
4    android:versionCode="4"
5    android:versionName="4.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->Z:\alwan6\app\src\main\AndroidManifest.xml:5:5-67
11-->Z:\alwan6\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission
12-->Z:\alwan6\app\src\main\AndroidManifest.xml:6:5-7:38
13        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
13-->Z:\alwan6\app\src\main\AndroidManifest.xml:6:22-78
14        android:maxSdkVersion="28" />
14-->Z:\alwan6\app\src\main\AndroidManifest.xml:7:9-35
15    <uses-permission
15-->Z:\alwan6\app\src\main\AndroidManifest.xml:8:5-9:38
16        android:name="android.permission.READ_EXTERNAL_STORAGE"
16-->Z:\alwan6\app\src\main\AndroidManifest.xml:8:22-77
17        android:maxSdkVersion="32" />
17-->Z:\alwan6\app\src\main\AndroidManifest.xml:9:9-35
18    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
18-->Z:\alwan6\app\src\main\AndroidManifest.xml:10:5-76
18-->Z:\alwan6\app\src\main\AndroidManifest.xml:10:22-74
19    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
19-->Z:\alwan6\app\src\main\AndroidManifest.xml:11:5-79
19-->Z:\alwan6\app\src\main\AndroidManifest.xml:11:22-76
20
21    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
22    <permission
22-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:7:5-9:47
23        android:name="com.alwan.kids2025.permission.C2D_MESSAGE"
23-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:8:9-63
24        android:protectionLevel="signature" />
24-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:9:9-44
25
26    <uses-permission android:name="com.alwan.kids2025.permission.C2D_MESSAGE" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
26-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:5-79
26-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:22-76
27    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- Required, makes sure notifications are delivered on time. -->
27-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:18:5-82
27-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:18:22-79
28    <uses-permission android:name="android.permission.WAKE_LOCK" />
28-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:19:5-68
28-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:19:22-65
29    <!--
30 Required so the device vibrates on receiving a push notification.
31         Vibration settings of the device still apply.
32    -->
33    <uses-permission android:name="android.permission.VIBRATE" />
33-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:24:5-66
33-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:24:22-63
34    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
34-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:25:5-79
34-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:25:22-76
35    <!--
36 Use to restore notifications the user hasn't interacted with.
37         They could be missed notifications if the user reboots their device if this isn't in place.
38    -->
39    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- START: ShortcutBadger -->
39-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:30:5-81
39-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:30:22-78
40    <!-- Samsung -->
41    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
41-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:5-86
41-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:22-83
42    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
42-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:5-87
42-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:22-84
43    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
43-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:5-81
43-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:22-78
44    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
44-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:5-83
44-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:22-80
45    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
45-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:5-88
45-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:22-85
46    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
46-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:5-92
46-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:22-89
47    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
47-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:5-84
47-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:22-81
48    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
48-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:5-83
48-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:22-80
49    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
49-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:5-91
49-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:22-88
50    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
50-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:5-92
50-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:22-89
51    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
51-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:5-93
51-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:22-90
52    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
52-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:5-73
52-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:22-70
53    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
53-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:5-82
53-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:22-79
54    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
54-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:5-83
54-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:22-80
55    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
55-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:5-88
55-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:22-85
56    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
56-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:5-89
56-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:22-86
57    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
57-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:27:5-82
57-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:27:22-79
58    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
58-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:28:5-88
58-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:28:22-85
59    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
59-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:5-83
59-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:22-80
60    <queries>
60-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:35:5-68:15
61
62        <!-- For browser content -->
63        <intent>
63-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:38:9-44:18
64            <action android:name="android.intent.action.VIEW" />
64-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
64-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
65
66            <category android:name="android.intent.category.BROWSABLE" />
66-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:13-74
66-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:23-71
67
68            <data android:scheme="https" />
68-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
68-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
69        </intent>
70        <!-- End of browser content -->
71        <!-- For CustomTabsService -->
72        <intent>
72-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:47:9-49:18
73            <action android:name="android.support.customtabs.action.CustomTabsService" />
73-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:13-90
73-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:21-87
74        </intent>
75        <!-- End of CustomTabsService -->
76        <!-- For MRAID capabilities -->
77        <intent>
77-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:52:9-56:18
78            <action android:name="android.intent.action.INSERT" />
78-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:13-67
78-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:21-64
79
80            <data android:mimeType="vnd.android.cursor.dir/event" />
80-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
81        </intent>
82        <intent>
82-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:57:9-61:18
83            <action android:name="android.intent.action.VIEW" />
83-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
83-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
84
85            <data android:scheme="sms" />
85-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
85-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
86        </intent>
87        <intent>
87-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:62:9-66:18
88            <action android:name="android.intent.action.DIAL" />
88-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:13-65
88-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:21-62
89
90            <data android:path="tel:" />
90-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
91        </intent>
92        <!-- End of MRAID capabilities -->
93    </queries> <!-- Permission will be merged into the manifest of the hosting app. -->
94    <!-- Is required to launch foreground extraction service for targetSdkVersion 28+. -->
95    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- Is required to launch foreground extraction service for targetSdkVersion 34+. -->
95-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:14:5-77
95-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:14:22-74
96    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
96-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:16:5-87
96-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:16:22-84
97    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
97-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
97-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
98
99    <permission
99-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
100        android:name="com.alwan.kids2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
100-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
101        android:protectionLevel="signature" />
101-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
102
103    <uses-permission android:name="com.alwan.kids2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
103-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
103-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
104
105    <application
105-->Z:\alwan6\app\src\main\AndroidManifest.xml:14:5-98:19
106        android:allowBackup="false"
106-->Z:\alwan6\app\src\main\AndroidManifest.xml:15:9-36
107        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
107-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980eaeb5be19d054e57b3fb1dced5a4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
108        android:debuggable="true"
109        android:extractNativeLibs="true"
110        android:icon="@drawable/logo"
110-->Z:\alwan6\app\src\main\AndroidManifest.xml:16:9-38
111        android:label="@string/app_name"
111-->Z:\alwan6\app\src\main\AndroidManifest.xml:17:9-41
112        android:largeHeap="true"
112-->Z:\alwan6\app\src\main\AndroidManifest.xml:19:9-33
113        android:requestLegacyExternalStorage="true"
113-->Z:\alwan6\app\src\main\AndroidManifest.xml:21:9-52
114        android:supportsRtl="true"
114-->Z:\alwan6\app\src\main\AndroidManifest.xml:18:9-35
115        android:testOnly="true"
116        android:theme="@style/AppTheme" >
116-->Z:\alwan6\app\src\main\AndroidManifest.xml:20:9-40
117        <meta-data
117-->Z:\alwan6\app\src\main\AndroidManifest.xml:22:9-100
118            android:name="google_analytics_adid_collection_enabled"
118-->Z:\alwan6\app\src\main\AndroidManifest.xml:22:20-75
119            android:value="false" />
119-->Z:\alwan6\app\src\main\AndroidManifest.xml:22:76-97
120        <!-- Removed duplicate OneSignal App ID meta-data entry -->
121
122        <meta-data
122-->Z:\alwan6\app\src\main\AndroidManifest.xml:25:9-27:69
123            android:name="com.google.android.gms.ads.APPLICATION_ID"
123-->Z:\alwan6\app\src\main\AndroidManifest.xml:26:13-69
124            android:value="ca-app-pub-7841751633097845~3195890380" />
124-->Z:\alwan6\app\src\main\AndroidManifest.xml:27:13-67
125
126        <property
126-->Z:\alwan6\app\src\main\AndroidManifest.xml:28:9-32:48
127            android:name="android.adservices.AD_SERVICES_CONFIG"
127-->Z:\alwan6\app\src\main\AndroidManifest.xml:30:13-65
128            android:resource="@xml/gma_ad_services_config" />
128-->Z:\alwan6\app\src\main\AndroidManifest.xml:31:13-59
129
130        <activity
130-->Z:\alwan6\app\src\main\AndroidManifest.xml:33:9-36:59
131            android:name="com.alwan.kids2025.MainActivityClean"
131-->Z:\alwan6\app\src\main\AndroidManifest.xml:34:13-64
132            android:exported="true"
132-->Z:\alwan6\app\src\main\AndroidManifest.xml:35:13-36
133            android:theme="@style/AppTheme.NoActionBar" />
133-->Z:\alwan6\app\src\main\AndroidManifest.xml:36:13-56
134
135        <!-- Keep Categories for backward compatibility -->
136        <activity
136-->Z:\alwan6\app\src\main\AndroidManifest.xml:41:9-44:40
137            android:name="com.alwan.kids2025.Categories"
137-->Z:\alwan6\app\src\main\AndroidManifest.xml:42:13-57
138            android:exported="false"
138-->Z:\alwan6\app\src\main\AndroidManifest.xml:44:13-37
139            android:theme="@style/AppTheme.NoActionBar" />
139-->Z:\alwan6\app\src\main\AndroidManifest.xml:43:13-56
140        <activity
140-->Z:\alwan6\app\src\main\AndroidManifest.xml:45:9-47:59
141            android:name="com.alwan.kids2025.CategoryItems"
141-->Z:\alwan6\app\src\main\AndroidManifest.xml:46:13-60
142            android:theme="@style/AppTheme.NoActionBar" />
142-->Z:\alwan6\app\src\main\AndroidManifest.xml:47:13-56
143        <activity
143-->Z:\alwan6\app\src\main\AndroidManifest.xml:48:9-57:20
144            android:name="com.alwan.kids2025.Splash"
144-->Z:\alwan6\app\src\main\AndroidManifest.xml:50:13-53
145            android:exported="true"
145-->Z:\alwan6\app\src\main\AndroidManifest.xml:49:13-36
146            android:theme="@style/AppTheme.NoActionBar" >
146-->Z:\alwan6\app\src\main\AndroidManifest.xml:51:13-56
147            <intent-filter>
147-->Z:\alwan6\app\src\main\AndroidManifest.xml:52:13-56:29
148                <action android:name="android.intent.action.MAIN" />
148-->Z:\alwan6\app\src\main\AndroidManifest.xml:53:17-69
148-->Z:\alwan6\app\src\main\AndroidManifest.xml:53:25-66
149
150                <category android:name="android.intent.category.LAUNCHER" />
150-->Z:\alwan6\app\src\main\AndroidManifest.xml:55:17-77
150-->Z:\alwan6\app\src\main\AndroidManifest.xml:55:27-74
151            </intent-filter>
152        </activity>
153
154        <!-- New Activities -->
155        <activity
155-->Z:\alwan6\app\src\main\AndroidManifest.xml:60:9-63:74
156            android:name="com.alwan.kids2025.AboutActivity"
156-->Z:\alwan6\app\src\main\AndroidManifest.xml:61:13-60
157            android:parentActivityName="com.alwan.kids2025.Categories"
157-->Z:\alwan6\app\src\main\AndroidManifest.xml:63:13-71
158            android:theme="@style/AppTheme.NoActionBar" />
158-->Z:\alwan6\app\src\main\AndroidManifest.xml:62:13-56
159        <activity
159-->Z:\alwan6\app\src\main\AndroidManifest.xml:64:9-67:74
160            android:name="com.alwan.kids2025.SettingsActivity"
160-->Z:\alwan6\app\src\main\AndroidManifest.xml:65:13-63
161            android:parentActivityName="com.alwan.kids2025.Categories"
161-->Z:\alwan6\app\src\main\AndroidManifest.xml:67:13-71
162            android:theme="@style/AppTheme.NoActionBar" />
162-->Z:\alwan6\app\src\main\AndroidManifest.xml:66:13-56
163        <activity
163-->Z:\alwan6\app\src\main\AndroidManifest.xml:69:9-72:74
164            android:name="com.alwan.kids2025.FavoritesActivity"
164-->Z:\alwan6\app\src\main\AndroidManifest.xml:70:13-64
165            android:parentActivityName="com.alwan.kids2025.Categories"
165-->Z:\alwan6\app\src\main\AndroidManifest.xml:72:13-71
166            android:theme="@style/AppTheme.NoActionBar" />
166-->Z:\alwan6\app\src\main\AndroidManifest.xml:71:13-56
167        <activity
167-->Z:\alwan6\app\src\main\AndroidManifest.xml:73:9-76:74
168            android:name="com.alwan.kids2025.MoreActivity"
168-->Z:\alwan6\app\src\main\AndroidManifest.xml:74:13-59
169            android:parentActivityName="com.alwan.kids2025.Categories"
169-->Z:\alwan6\app\src\main\AndroidManifest.xml:76:13-71
170            android:theme="@style/AppTheme.NoActionBar" />
170-->Z:\alwan6\app\src\main\AndroidManifest.xml:75:13-56
171
172        <service
172-->Z:\alwan6\app\src\main\AndroidManifest.xml:79:9-86:19
173            android:name="com.alwan.kids2025.MyFirebaseMessagingService"
173-->Z:\alwan6\app\src\main\AndroidManifest.xml:80:13-73
174            android:enabled="true"
174-->Z:\alwan6\app\src\main\AndroidManifest.xml:81:13-35
175            android:exported="true" >
175-->Z:\alwan6\app\src\main\AndroidManifest.xml:82:13-36
176            <intent-filter>
176-->Z:\alwan6\app\src\main\AndroidManifest.xml:83:13-85:29
177                <action android:name="com.google.firebase.MESSAGING_EVENT" />
177-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:17-78
177-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:25-75
178            </intent-filter>
179        </service>
180
181        <!-- FileProvider for sharing images -->
182        <provider
183            android:name="androidx.core.content.FileProvider"
183-->Z:\alwan6\app\src\main\AndroidManifest.xml:90:13-62
184            android:authorities="com.alwan.kids2025.fileprovider"
184-->Z:\alwan6\app\src\main\AndroidManifest.xml:91:13-64
185            android:exported="false"
185-->Z:\alwan6\app\src\main\AndroidManifest.xml:92:13-37
186            android:grantUriPermissions="true" >
186-->Z:\alwan6\app\src\main\AndroidManifest.xml:93:13-47
187            <meta-data
187-->Z:\alwan6\app\src\main\AndroidManifest.xml:94:13-96:54
188                android:name="android.support.FILE_PROVIDER_PATHS"
188-->Z:\alwan6\app\src\main\AndroidManifest.xml:95:17-67
189                android:resource="@xml/file_paths" />
189-->Z:\alwan6\app\src\main\AndroidManifest.xml:96:17-51
190        </provider>
191
192        <receiver
192-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:50:9-61:20
193            android:name="com.onesignal.FCMBroadcastReceiver"
193-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:51:13-62
194            android:exported="true"
194-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:52:13-36
195            android:permission="com.google.android.c2dm.permission.SEND" >
195-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:53:13-73
196
197            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
198            <intent-filter android:priority="999" >
198-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:13-60:29
198-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:28-50
199                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
199-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
199-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
200
201                <category android:name="com.alwan.kids2025" />
201-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:17-61
201-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:27-58
202            </intent-filter>
203        </receiver>
204
205        <service
205-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:63:9-69:19
206            android:name="com.onesignal.HmsMessageServiceOneSignal"
206-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:64:13-68
207            android:exported="false" >
207-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:65:13-37
208            <intent-filter>
208-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:66:13-68:29
209                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
209-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:17-81
209-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:25-78
210            </intent-filter>
211        </service>
212
213        <activity
213-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:71:9-79:20
214            android:name="com.onesignal.NotificationOpenedActivityHMS"
214-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:72:13-71
215            android:exported="true"
215-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:73:13-36
216            android:noHistory="true"
216-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:74:13-37
217            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
217-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:75:13-72
218            <intent-filter>
218-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:76:13-78:29
219                <action android:name="android.intent.action.VIEW" />
219-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
219-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
220            </intent-filter>
221        </activity>
222
223        <service
223-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:81:9-83:40
224            android:name="com.onesignal.FCMIntentService"
224-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:82:13-58
225            android:exported="false" />
225-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:83:13-37
226        <service
226-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:84:9-87:72
227            android:name="com.onesignal.FCMIntentJobService"
227-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:85:13-61
228            android:exported="false"
228-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:86:13-37
229            android:permission="android.permission.BIND_JOB_SERVICE" />
229-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:87:13-69
230        <service
230-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:88:9-91:43
231            android:name="com.onesignal.SyncService"
231-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:89:13-53
232            android:exported="false"
232-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:90:13-37
233            android:stopWithTask="true" />
233-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:91:13-40
234        <service
234-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:92:9-95:72
235            android:name="com.onesignal.SyncJobService"
235-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:93:13-56
236            android:exported="false"
236-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:94:13-37
237            android:permission="android.permission.BIND_JOB_SERVICE" />
237-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:95:13-69
238
239        <activity
239-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:97:9-100:75
240            android:name="com.onesignal.PermissionsActivity"
240-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:98:13-61
241            android:exported="false"
241-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:99:13-37
242            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
242-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:100:13-72
243
244        <receiver
244-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:102:9-104:39
245            android:name="com.onesignal.NotificationDismissReceiver"
245-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:103:13-69
246            android:exported="true" />
246-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:104:13-36
247        <receiver
247-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:105:9-112:20
248            android:name="com.onesignal.BootUpReceiver"
248-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:106:13-56
249            android:exported="true" >
249-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:107:13-36
250            <intent-filter>
250-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:108:13-111:29
251                <action android:name="android.intent.action.BOOT_COMPLETED" />
251-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
251-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
252                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
252-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:17-82
252-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:25-79
253            </intent-filter>
254        </receiver>
255        <receiver
255-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:113:9-119:20
256            android:name="com.onesignal.UpgradeReceiver"
256-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:114:13-57
257            android:exported="true" >
257-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:115:13-36
258            <intent-filter>
258-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:116:13-118:29
259                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
259-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:17-84
259-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:25-81
260            </intent-filter>
261        </receiver>
262
263        <activity
263-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:121:9-127:75
264            android:name="com.onesignal.NotificationOpenedReceiver"
264-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:122:13-68
265            android:excludeFromRecents="true"
265-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:123:13-46
266            android:exported="true"
266-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:124:13-36
267            android:noHistory="true"
267-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:125:13-37
268            android:taskAffinity=""
268-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:126:13-36
269            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
269-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:127:13-72
270        <activity
270-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:128:9-133:75
271            android:name="com.onesignal.NotificationOpenedReceiverAndroid22AndOlder"
271-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:129:13-85
272            android:excludeFromRecents="true"
272-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:130:13-46
273            android:exported="true"
273-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:131:13-36
274            android:noHistory="true"
274-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:132:13-37
275            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
275-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:133:13-72
276        <activity
276-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:73:9-78:43
277            android:name="com.google.android.gms.ads.AdActivity"
277-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:74:13-65
278            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
278-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:75:13-122
279            android:exported="false"
279-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:76:13-37
280            android:theme="@android:style/Theme.Translucent" />
280-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:77:13-61
281
282        <provider
282-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:80:9-85:43
283            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
283-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:81:13-76
284            android:authorities="com.alwan.kids2025.mobileadsinitprovider"
284-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:82:13-73
285            android:exported="false"
285-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:83:13-37
286            android:initOrder="100" />
286-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:84:13-36
287
288        <service
288-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:87:9-91:43
289            android:name="com.google.android.gms.ads.AdService"
289-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:88:13-64
290            android:enabled="true"
290-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:89:13-35
291            android:exported="false" />
291-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:90:13-37
292
293        <activity
293-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:93:9-97:43
294            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
294-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:94:13-82
295            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
295-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:95:13-122
296            android:exported="false" />
296-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:96:13-37
297        <activity
297-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:98:9-105:43
298            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
298-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:99:13-82
299            android:excludeFromRecents="true"
299-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:100:13-46
300            android:exported="false"
300-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:101:13-37
301            android:launchMode="singleTask"
301-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:102:13-44
302            android:taskAffinity=""
302-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:103:13-36
303            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- The services will be merged into the manifest of the hosting app. -->
303-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf6e027b1f0a3e8a0ae6ac21343cf403\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:104:13-72
304        <service
304-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:21:9-28:19
305            android:name="com.google.android.play.core.assetpacks.AssetPackExtractionService"
305-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:22:13-94
306            android:enabled="false"
306-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:23:13-36
307            android:exported="true" >
307-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:24:13-36
308            <meta-data
308-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:25:13-27:41
309                android:name="com.google.android.play.core.assetpacks.versionCode"
309-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:26:17-83
310                android:value="20300" />
310-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:27:17-38
311        </service>
312        <service
312-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:29:9-33:56
313            android:name="com.google.android.play.core.assetpacks.ExtractionForegroundService"
313-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:30:13-95
314            android:enabled="false"
314-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:31:13-36
315            android:exported="false"
315-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:32:13-37
316            android:foregroundServiceType="dataSync" />
316-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:33:13-53
317
318        <receiver
318-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:35:9-43:20
319            android:name="com.google.android.play.core.assetpacks.SessionStateBroadcastReceiver"
319-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:36:13-97
320            android:enabled="true"
320-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:37:13-35
321            android:exported="true"
321-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:38:13-36
322            android:permission="android.permission.INSTALL_PACKAGES" >
322-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:39:13-69
323            <intent-filter>
323-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:40:13-42:29
324                <action android:name="com.google.android.play.core.assetpacks.receiver.ACTION_SESSION_UPDATE" />
324-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:41:17-113
324-->[com.google.android.play:asset-delivery:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c3592c4073517de57b9ed945418e3a70\transformed\jetified-asset-delivery-2.3.0\AndroidManifest.xml:41:25-110
325            </intent-filter>
326        </receiver>
327        <receiver
327-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:29:9-40:20
328            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
328-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:30:13-78
329            android:exported="true"
329-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:31:13-36
330            android:permission="com.google.android.c2dm.permission.SEND" >
330-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:32:13-73
331            <intent-filter>
331-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:33:13-35:29
332                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
332-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
332-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
333            </intent-filter>
334
335            <meta-data
335-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:37:13-39:40
336                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
336-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:38:17-92
337                android:value="true" />
337-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:39:17-37
338        </receiver>
339        <!--
340             FirebaseMessagingService performs security checks at runtime,
341             but set to not exported to explicitly avoid allowing another app to call it.
342        -->
343        <service
343-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:46:9-53:19
344            android:name="com.google.firebase.messaging.FirebaseMessagingService"
344-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:47:13-82
345            android:directBootAware="true"
345-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:48:13-43
346            android:exported="false" >
346-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:49:13-37
347            <intent-filter android:priority="-500" >
347-->Z:\alwan6\app\src\main\AndroidManifest.xml:83:13-85:29
348                <action android:name="com.google.firebase.MESSAGING_EVENT" />
348-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:17-78
348-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:25-75
349            </intent-filter>
350        </service>
351        <service
351-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:54:9-63:19
352            android:name="com.google.firebase.components.ComponentDiscoveryService"
352-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:55:13-84
353            android:directBootAware="true"
353-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
354            android:exported="false" >
354-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:56:13-37
355            <meta-data
355-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:57:13-59:85
356                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
356-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:58:17-122
357                android:value="com.google.firebase.components.ComponentRegistrar" />
357-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:59:17-82
358            <meta-data
358-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:60:13-62:85
359                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
359-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:61:17-119
360                android:value="com.google.firebase.components.ComponentRegistrar" />
360-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3bba6aeb63f8923ad18faf4732ba85ec\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:62:17-82
361            <meta-data
361-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a6a9eeb64f60b995434706b8f1fbad3\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
362                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
362-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a6a9eeb64f60b995434706b8f1fbad3\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
363                android:value="com.google.firebase.components.ComponentRegistrar" />
363-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a6a9eeb64f60b995434706b8f1fbad3\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
364            <meta-data
364-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
365                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
365-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
366                android:value="com.google.firebase.components.ComponentRegistrar" />
366-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
367            <meta-data
367-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
368                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
368-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
369                android:value="com.google.firebase.components.ComponentRegistrar" />
369-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39bb0ed41c813153b1b378d7e0ade4e7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
370            <meta-data
370-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe74116495f61bbb5439e8eb676f6bb\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
371                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
371-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe74116495f61bbb5439e8eb676f6bb\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
372                android:value="com.google.firebase.components.ComponentRegistrar" />
372-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe74116495f61bbb5439e8eb676f6bb\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
373            <meta-data
373-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
374                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
374-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
375                android:value="com.google.firebase.components.ComponentRegistrar" />
375-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
376            <meta-data
376-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33fb70538b965f1a9670519e671fc5e1\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
377                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
377-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33fb70538b965f1a9670519e671fc5e1\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
378                android:value="com.google.firebase.components.ComponentRegistrar" />
378-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33fb70538b965f1a9670519e671fc5e1\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
379        </service>
380
381        <provider
381-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
382            android:name="com.google.firebase.provider.FirebaseInitProvider"
382-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
383            android:authorities="com.alwan.kids2025.firebaseinitprovider"
383-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
384            android:directBootAware="true"
384-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
385            android:exported="false"
385-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
386            android:initOrder="100" />
386-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\513d0edc5e9e313a9e6b26d2119a2807\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
387
388        <receiver
388-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
389            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
389-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
390            android:enabled="true"
390-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
391            android:exported="false" >
391-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
392        </receiver>
393
394        <service
394-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
395            android:name="com.google.android.gms.measurement.AppMeasurementService"
395-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
396            android:enabled="true"
396-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
397            android:exported="false" />
397-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
398        <service
398-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
399            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
399-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
400            android:enabled="true"
400-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
401            android:exported="false"
401-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
402            android:permission="android.permission.BIND_JOB_SERVICE" />
402-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\800332fa5659b10311e8de18e0bce7bf\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
403
404        <activity
404-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d4e9d128fcd43e5a6e86ce4f6eb5926\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
405            android:name="com.google.android.gms.common.api.GoogleApiActivity"
405-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d4e9d128fcd43e5a6e86ce4f6eb5926\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
406            android:exported="false"
406-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d4e9d128fcd43e5a6e86ce4f6eb5926\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
407            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
407-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d4e9d128fcd43e5a6e86ce4f6eb5926\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
408
409        <provider
409-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:29:9-37:20
410            android:name="androidx.startup.InitializationProvider"
410-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:30:13-67
411            android:authorities="com.alwan.kids2025.androidx-startup"
411-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:31:13-68
412            android:exported="false" >
412-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:32:13-37
413            <meta-data
413-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:34:13-36:52
414                android:name="androidx.work.WorkManagerInitializer"
414-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:35:17-68
415                android:value="androidx.startup" />
415-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:36:17-49
416            <meta-data
416-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d40185c6d6f9ccddea7a3fb92398952c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
417                android:name="androidx.emoji2.text.EmojiCompatInitializer"
417-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d40185c6d6f9ccddea7a3fb92398952c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
418                android:value="androidx.startup" />
418-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d40185c6d6f9ccddea7a3fb92398952c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
419            <meta-data
419-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\472ac2837326f25df4d2cf2853c19477\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
420                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
420-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\472ac2837326f25df4d2cf2853c19477\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
421                android:value="androidx.startup" />
421-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\472ac2837326f25df4d2cf2853c19477\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
422            <meta-data
422-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
423                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
423-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
424                android:value="androidx.startup" />
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
425        </provider>
426
427        <service
427-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
428            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
428-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
429            android:directBootAware="false"
429-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
430            android:enabled="@bool/enable_system_alarm_service_default"
430-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
431            android:exported="false" />
431-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
432        <service
432-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
433            android:name="androidx.work.impl.background.systemjob.SystemJobService"
433-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
434            android:directBootAware="false"
434-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
435            android:enabled="@bool/enable_system_job_service_default"
435-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
436            android:exported="true"
436-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
437            android:permission="android.permission.BIND_JOB_SERVICE" />
437-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
438        <service
438-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
439            android:name="androidx.work.impl.foreground.SystemForegroundService"
439-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
440            android:directBootAware="false"
440-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
441            android:enabled="@bool/enable_system_foreground_service_default"
441-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
442            android:exported="false" />
442-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
443
444        <receiver
444-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
445            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
445-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
446            android:directBootAware="false"
446-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
447            android:enabled="true"
447-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
448            android:exported="false" />
448-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
449        <receiver
449-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
450            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
450-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
451            android:directBootAware="false"
451-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
452            android:enabled="false"
452-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
453            android:exported="false" >
453-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
454            <intent-filter>
454-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
455                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
455-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
455-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
456                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
456-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
456-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
457            </intent-filter>
458        </receiver>
459        <receiver
459-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
460            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
460-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
461            android:directBootAware="false"
461-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
462            android:enabled="false"
462-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
463            android:exported="false" >
463-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
464            <intent-filter>
464-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
465                <action android:name="android.intent.action.BATTERY_OKAY" />
465-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
465-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
466                <action android:name="android.intent.action.BATTERY_LOW" />
466-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
466-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
467            </intent-filter>
468        </receiver>
469        <receiver
469-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
470            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
470-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
471            android:directBootAware="false"
471-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
472            android:enabled="false"
472-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
473            android:exported="false" >
473-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
474            <intent-filter>
474-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
475                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
475-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
475-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
476                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
476-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
476-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
477            </intent-filter>
478        </receiver>
479        <receiver
479-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
480            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
480-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
481            android:directBootAware="false"
481-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
482            android:enabled="false"
482-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
483            android:exported="false" >
483-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
484            <intent-filter>
484-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
485                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
485-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
485-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
486            </intent-filter>
487        </receiver>
488        <receiver
488-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
489            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
489-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
490            android:directBootAware="false"
490-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
491            android:enabled="false"
491-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
492            android:exported="false" >
492-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
493            <intent-filter>
493-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
494                <action android:name="android.intent.action.BOOT_COMPLETED" />
494-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
494-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\60531700a9238cce91cf93d1b1fc03da\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
495                <action android:name="android.intent.action.TIME_SET" />
495-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
495-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
496                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
496-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
496-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
497            </intent-filter>
498        </receiver>
499        <receiver
499-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
500            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
500-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
501            android:directBootAware="false"
501-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
502            android:enabled="@bool/enable_system_alarm_service_default"
502-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
503            android:exported="false" >
503-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
504            <intent-filter>
504-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
505                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
505-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
505-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
506            </intent-filter>
507        </receiver>
508        <receiver
508-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
509            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
509-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
510            android:directBootAware="false"
510-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
511            android:enabled="true"
511-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
512            android:exported="true"
512-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
513            android:permission="android.permission.DUMP" >
513-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
514            <intent-filter>
514-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
515                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
515-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
515-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\169645896d69d0c9bb0dc9a4ca962770\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
516            </intent-filter>
517        </receiver>
518
519        <uses-library
519-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
520            android:name="androidx.window.extensions"
520-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
521            android:required="false" />
521-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
522        <uses-library
522-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
523            android:name="androidx.window.sidecar"
523-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
524            android:required="false" />
524-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b867664b69323af9a6902ce8a7ddca5\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
525        <uses-library
525-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\811dec69550609acf2352cb1e05b0175\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
526            android:name="android.ext.adservices"
526-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\811dec69550609acf2352cb1e05b0175\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
527            android:required="false" />
527-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\811dec69550609acf2352cb1e05b0175\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
528
529        <meta-data
529-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c41e8149bb8e825c8201b1dfddeb0e8d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
530            android:name="com.google.android.gms.version"
530-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c41e8149bb8e825c8201b1dfddeb0e8d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
531            android:value="@integer/google_play_services_version" />
531-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c41e8149bb8e825c8201b1dfddeb0e8d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
532
533        <service
533-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77f8cb0a36d0c54e3a8e1088e63c47af\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
534            android:name="androidx.room.MultiInstanceInvalidationService"
534-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77f8cb0a36d0c54e3a8e1088e63c47af\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
535            android:directBootAware="true"
535-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77f8cb0a36d0c54e3a8e1088e63c47af\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
536            android:exported="false" />
536-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77f8cb0a36d0c54e3a8e1088e63c47af\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
537
538        <receiver
538-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
539            android:name="androidx.profileinstaller.ProfileInstallReceiver"
539-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
540            android:directBootAware="false"
540-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
541            android:enabled="true"
541-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
542            android:exported="true"
542-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
543            android:permission="android.permission.DUMP" >
543-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
544            <intent-filter>
544-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
545                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
545-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
545-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
546            </intent-filter>
547            <intent-filter>
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
548                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
548-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
548-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
549            </intent-filter>
550            <intent-filter>
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
551                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
551-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
551-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
552            </intent-filter>
553            <intent-filter>
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
554                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
554-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
554-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3658630fa452d8495662e34580b3610\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
555            </intent-filter>
556        </receiver>
557
558        <service
558-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
559            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
559-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
560            android:exported="false" >
560-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
561            <meta-data
561-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
562                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
562-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
563                android:value="cct" />
563-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\148bc18c93b12c69cfed2eeb49d5e167\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
564        </service>
565        <service
565-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
566            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
566-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
567            android:exported="false"
567-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
568            android:permission="android.permission.BIND_JOB_SERVICE" >
568-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
569        </service>
570
571        <receiver
571-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
572            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
572-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
573            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
573-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\322149491b7d3e72f68724435acdf034\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
574        <activity
574-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\07b3b08723cfc757f0a46648ed4d6b31\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
575            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
575-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\07b3b08723cfc757f0a46648ed4d6b31\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
576            android:exported="false"
576-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\07b3b08723cfc757f0a46648ed4d6b31\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
577            android:stateNotNeeded="true"
577-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\07b3b08723cfc757f0a46648ed4d6b31\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
578            android:theme="@style/Theme.PlayCore.Transparent" />
578-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\07b3b08723cfc757f0a46648ed4d6b31\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
579    </application>
580
581</manifest>
