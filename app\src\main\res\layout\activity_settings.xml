<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modern_background">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/modern_gradient_background"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:paddingBottom="96dp">

            <!-- Sound Settings -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_volume_modern"
                            android:tint="@color/modern_primary" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/sound_settings"
                            android:textColor="@color/modern_primary"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <!-- Sound Effects Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/sound_effects"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_sound_effects"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:theme="@style/ModernSwitchStyle" />

                    </LinearLayout>

                    <!-- Background Music Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/background_music"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_background_music"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:theme="@style/ModernSwitchStyle" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Display Settings -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_display_modern"
                            android:tint="@color/cartoons_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/display_settings"
                            android:textColor="@color/cartoons_color"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <!-- Auto Save Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/auto_save"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_auto_save"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:theme="@style/ModernSwitchStyle" />

                    </LinearLayout>

                    <!-- Show Grid Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/show_grid"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_show_grid"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            android:theme="@style/ModernSwitchStyle" />

                    </LinearLayout>

                    <!-- Dark Mode Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/dark_mode"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_dark_mode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            android:theme="@style/ModernSwitchStyle" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Language & Quality Settings -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_settings_modern"
                            android:tint="@color/animals_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/language_quality"
                            android:textColor="@color/animals_color"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <!-- Language Setting -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/language_card"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:padding="16dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/app_language"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:fontFamily="@font/blabeloo" />

                            <TextView
                                android:id="@+id/language_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/language_english"
                                android:textColor="@color/animals_color"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo"
                                android:layout_marginEnd="8dp" />

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:src="@drawable/ic_arrow_forward"
                                android:tint="@color/text_secondary" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                    <!-- Image Quality Setting -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/quality_card"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:padding="16dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/image_quality"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:fontFamily="@font/blabeloo" />

                            <TextView
                                android:id="@+id/quality_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/quality_high"
                                android:textColor="@color/animals_color"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo"
                                android:layout_marginEnd="8dp" />

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:src="@drawable/ic_arrow_forward"
                                android:tint="@color/text_secondary" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Privacy Settings -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_privacy_modern"
                            android:tint="@color/nature_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/privacy_security"
                            android:textColor="@color/nature_color"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <!-- Privacy Policy Button -->
                    <LinearLayout
                        android:id="@+id/btn_privacy_policy"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@drawable/settings_item_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="16dp"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/privacy_policy"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Reset Ads Button -->
                    <LinearLayout
                        android:id="@+id/btn_reset_ads"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@drawable/settings_item_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/reset_ads"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_refresh_modern"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- App Info -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_info_modern"
                            android:tint="@color/modern_accent" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/app_info"
                            android:textColor="@color/modern_accent"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <!-- Version Info -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/app_version"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/app_version_text"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <!-- Build Info -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/build_number"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/build_date"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Advanced Features Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_star_modern"
                            android:tint="@color/foods_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/advanced_features"
                            android:textColor="@color/foods_color"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <!-- Vibration Feedback Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/vibration_on_coloring"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_vibration"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            android:theme="@style/ModernSwitchStyle" />

                    </LinearLayout>

                    <!-- Animation Speed Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/fast_animations"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_fast_animations"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:theme="@style/ModernSwitchStyle" />

                    </LinearLayout>

                    <!-- Auto Color Suggestions Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/automatic_color_suggestions"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_color_suggestions"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:theme="@style/ModernSwitchStyle" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Navigation -->
    <include
        layout="@layout/simple_bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
