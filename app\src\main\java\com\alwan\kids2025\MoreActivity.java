package com.alwan.kids2025;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.widget.LinearLayout;

public class MoreActivity extends BaseLocalizedActivity {

    private LinearLayout moreSettings;
    private LinearLayout moreAbout;
    private LinearLayout moreShare;
    private LinearLayout moreRate;
    private LinearLayout morePrivacy;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_more);

        // Initialize views
        initViews();

        // Setup listeners
        setupListeners();

        // Setup bottom navigation
        BottomNavigationHelperSimple.setupBottomNavigation(this, BottomNavigationHelperSimple.TAB_MORE);
    }

    private void initViews() {
        moreSettings = findViewById(R.id.more_settings);
        moreAbout = findViewById(R.id.more_about);
        moreShare = findViewById(R.id.more_share);
        moreRate = findViewById(R.id.more_rate);
        morePrivacy = findViewById(R.id.more_privacy);
    }

    private void setupListeners() {
        moreSettings.setOnClickListener(v -> {
            Intent intent = new Intent(this, SettingsActivity.class);
            startActivity(intent);
        });

        moreAbout.setOnClickListener(v -> {
            Intent intent = new Intent(this, AboutActivity.class);
            startActivity(intent);
        });

        moreShare.setOnClickListener(v -> {
            shareTextUrl();
        });

        moreRate.setOnClickListener(v -> {
            String packageName = getPackageName();
            Intent intent = new Intent(Intent.ACTION_VIEW,
                Uri.parse("https://play.google.com/store/apps/details?id=" + packageName));
            startActivity(intent);
        });

        morePrivacy.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_VIEW,
                Uri.parse("https://sites.google.com/view/colors-kids"));
            startActivity(intent);
        });
    }



    private void shareTextUrl() {
        String packageName = getPackageName();
        Intent share = new Intent(Intent.ACTION_SEND);
        share.setType("text/plain");
        share.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        // Use localized share text based on current language
        String currentLanguage = getResources().getConfiguration().locale.getLanguage();
        String shareText;
        if ("ar".equals(currentLanguage)) {
            shareText = getString(R.string.share_text_arabic);
        } else {
            shareText = getString(R.string.share_text_english);
        }

        share.putExtra(Intent.EXTRA_TEXT, shareText +
            "https://play.google.com/store/apps/details?id=" + packageName);
        startActivity(Intent.createChooser(share, getString(R.string.share_chooser_title)));
    }

    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return true;
    }

    @Override
    protected boolean useToolbar() {
        return false;
    }

    protected boolean useDrawerToggle() {
        return false;
    }
}
