package com.alwan.kids2025;

import android.graphics.Bitmap;

/**
 * Created by AHED.OMN on 20/05/2025.
 * <EMAIL>
 */

public class ImageItem {
    private Bitmap image;
    private String imageName;
    private int categoryCode;
    private int position;

    public ImageItem(Bitmap image) {
        super();
        this.image = image;
    }

    public ImageItem() {
        super();
    }

    public Bitmap getImage() {
        return image;
    }

    public void setImage(Bitmap image) {
        this.image = image;
    }

    public String getImageName() {
        return imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public int getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(int categoryCode) {
        this.categoryCode = categoryCode;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }
}
